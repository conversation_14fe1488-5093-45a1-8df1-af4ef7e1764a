/**
 * Unit tests for messageNavigator utility functions
 * Tests message highlighting, lazy loading, and scroll-to-message functionality
 */

const {
  buildIndex,
  buildRealIndex,
  findOffset,
  findMessageInfo,
  scrollTo,
  highlightMessage,
  clearHighlight,
  buildLazyLoadIndex,
  getVisibleRange,
  DEFAULT_PAGE_SIZE,
  HIGHLIGHT_CLASS,
  HIGHLIGHT_DURATION
} = require('../../src/frontend/utils/messageNavigator.js');

// Mock DOM environment for testing
const mockElement = (messageId, offsetTop = 0) => ({
  getBoundingClientRect: () => ({ top: offsetTop }),
  classList: {
    add: jest.fn(),
    remove: jest.fn()
  },
  setAttribute: jest.fn(),
  getAttribute: jest.fn(() => messageId)
});

const mockContainer = (scrollTop = 0, clientHeight = 400) => ({
  getBoundingClientRect: () => ({ top: 0, height: clientHeight }),
  scrollTop,
  clientHeight,
  scrollHeight: 1000,
  scrollTo: jest.fn(),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => [])
});

describe('messageNavigator', () => {
  describe('buildIndex', () => {
    test('builds index with synthetic offsets for testing', () => {
      const messages = [
        { message_id: 101, turn_id: 1, content: 'Hello' },
        { message_id: 102, turn_id: 1, content: 'Hi' },
        { message_id: 103, turn_id: 2, content: 'How are you?' }
      ];

      const index = buildIndex(messages);

      expect(index.size).toBe(3);
      expect(index.get(101)).toEqual({
        offset: 0,
        index: 0,
        turnId: 1,
        messageId: 101
      });
      expect(index.get(102)).toEqual({
        offset: 100,
        index: 1,
        turnId: 1,
        messageId: 102
      });
      expect(index.get(103)).toEqual({
        offset: 200,
        index: 2,
        turnId: 2,
        messageId: 103
      });
    });

    test('handles messages without message_id', () => {
      const messages = [
        { id: 201, content: 'Test' },
        { message_id: 202, content: 'Test2' }
      ];

      const index = buildIndex(messages);

      expect(index.size).toBe(2);
      expect(index.has(201)).toBe(true);
      expect(index.has(202)).toBe(true);
    });

    test('handles empty messages array', () => {
      const index = buildIndex([]);
      expect(index.size).toBe(0);
    });
  });

  describe('buildRealIndex', () => {
    test('builds index from actual DOM elements', () => {
      const messages = [
        { message_id: 101, content: 'Hello' },
        { message_id: 102, content: 'Hi' }
      ];

      const element1 = mockElement(101, 50);
      const element2 = mockElement(102, 150);
      
      const container = mockContainer();
      container.querySelector = jest.fn()
        .mockReturnValueOnce(element1)
        .mockReturnValueOnce(element2);

      const index = buildRealIndex(container, messages);

      expect(index.size).toBe(2);
      expect(container.querySelector).toHaveBeenCalledWith('[data-message-id="101"]');
      expect(container.querySelector).toHaveBeenCalledWith('[data-message-id="102"]');
      
      const info1 = index.get(101);
      expect(info1.offset).toBe(50);
      expect(info1.element).toBe(element1);
    });

    test('handles missing container', () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const index = buildRealIndex(null, messages);
      expect(index.size).toBe(0);
    });
  });

  describe('findOffset and findMessageInfo', () => {
    test('finds offset for existing message', () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const index = buildIndex(messages);

      expect(findOffset(index, 101)).toBe(0);
      expect(findMessageInfo(index, 101)).toEqual({
        offset: 0,
        index: 0,
        turnId: undefined,
        messageId: 101
      });
    });

    test('returns null for non-existent message', () => {
      const index = buildIndex([]);
      expect(findOffset(index, 999)).toBeNull();
      expect(findMessageInfo(index, 999)).toBeNull();
    });
  });

  describe('scrollTo', () => {
    test('scrolls to message with smooth behavior', async () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const index = buildIndex(messages);
      const container = mockContainer();

      const result = await scrollTo(container, 101, index);

      expect(container.scrollTo).toHaveBeenCalledWith({
        top: 0,
        behavior: 'smooth'
      });
      expect(result).toBe(0);
    });

    test('falls back to scrollTop if scrollTo fails', async () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const index = buildIndex(messages);
      const container = mockContainer();
      container.scrollTo = jest.fn(() => { throw new Error('scrollTo failed'); });

      const result = await scrollTo(container, 101, index);

      expect(container.scrollTop).toBe(0);
      expect(result).toBe(0);
    });

    test('returns offset when no container provided', async () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const index = buildIndex(messages);

      const result = await scrollTo(null, 101, index);
      expect(result).toBe(0);
    });

    test('returns null for non-existent message', async () => {
      const index = buildIndex([]);
      const container = mockContainer();

      const result = await scrollTo(container, 999, index);
      expect(result).toBeNull();
    });
  });

  describe('highlightMessage', () => {
    test('highlights message element', () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const element = mockElement(101);
      const container = mockContainer();
      container.querySelector = jest.fn().mockReturnValue(element);
      container.querySelectorAll = jest.fn().mockReturnValue([]);

      const index = buildIndex(messages);
      index.get(101).element = element;

      const result = highlightMessage(container, 101, index);

      expect(result).toBe(true);
      expect(element.classList.add).toHaveBeenCalledWith(HIGHLIGHT_CLASS);
    });

    test('clears existing highlights before highlighting', () => {
      const messages = [{ message_id: 101, content: 'Hello' }];
      const element = mockElement(101);
      const existingHighlight = mockElement(102);
      
      const container = mockContainer();
      container.querySelector = jest.fn().mockReturnValue(element);
      container.querySelectorAll = jest.fn().mockReturnValue([existingHighlight]);

      const index = buildIndex(messages);

      highlightMessage(container, 101, index);

      expect(existingHighlight.classList.remove).toHaveBeenCalledWith(HIGHLIGHT_CLASS);
      expect(element.classList.add).toHaveBeenCalledWith(HIGHLIGHT_CLASS);
    });

    test('returns false when message not found', () => {
      const container = mockContainer();
      container.querySelector = jest.fn().mockReturnValue(null);
      container.querySelectorAll = jest.fn().mockReturnValue([]);

      const index = buildIndex([]);

      const result = highlightMessage(container, 999, index);
      expect(result).toBe(false);
    });
  });

  describe('clearHighlight', () => {
    test('removes highlight class from all highlighted elements', () => {
      const element1 = mockElement(101);
      const element2 = mockElement(102);
      const container = mockContainer();
      container.querySelectorAll = jest.fn().mockReturnValue([element1, element2]);

      clearHighlight(container);

      expect(container.querySelectorAll).toHaveBeenCalledWith(`.${HIGHLIGHT_CLASS}`);
      expect(element1.classList.remove).toHaveBeenCalledWith(HIGHLIGHT_CLASS);
      expect(element2.classList.remove).toHaveBeenCalledWith(HIGHLIGHT_CLASS);
    });

    test('handles null container gracefully', () => {
      expect(() => clearHighlight(null)).not.toThrow();
    });
  });

  describe('buildLazyLoadIndex', () => {
    test('creates pagination index for large message list', () => {
      const messages = Array.from({ length: 125 }, (_, i) => ({
        message_id: i + 1,
        content: `Message ${i + 1}`
      }));

      const index = buildLazyLoadIndex(messages, 50);

      expect(index.totalMessages).toBe(125);
      expect(index.totalPages).toBe(3);
      expect(index.pageSize).toBe(50);
      expect(index.pages).toHaveLength(3);
      
      expect(index.pages[0].startIndex).toBe(0);
      expect(index.pages[0].endIndex).toBe(49);
      expect(index.pages[0].messages).toHaveLength(50);
      
      expect(index.pages[2].startIndex).toBe(100);
      expect(index.pages[2].endIndex).toBe(124);
      expect(index.pages[2].messages).toHaveLength(25);
    });

    test('handles small message list', () => {
      const messages = [
        { message_id: 1, content: 'Hello' },
        { message_id: 2, content: 'Hi' }
      ];

      const index = buildLazyLoadIndex(messages, 50);

      expect(index.totalMessages).toBe(2);
      expect(index.totalPages).toBe(1);
      expect(index.pages[0].messages).toHaveLength(2);
    });

    test('handles empty message list', () => {
      const index = buildLazyLoadIndex([], 50);

      expect(index.totalMessages).toBe(0);
      expect(index.totalPages).toBe(0);
      expect(index.pages).toHaveLength(0);
    });
  });

  describe('getVisibleRange', () => {
    test('returns messages within viewport', () => {
      const container = mockContainer(100, 400); // scrollTop: 100, height: 400
      const index = new Map([
        [101, { offset: 50, messageId: 101 }],   // Above viewport
        [102, { offset: 150, messageId: 102 }],  // In viewport
        [103, { offset: 300, messageId: 103 }],  // In viewport
        [104, { offset: 600, messageId: 104 }]   // Below viewport
      ]);

      const result = getVisibleRange(container, index, 0); // no buffer

      expect(result.visibleMessages).toHaveLength(2);
      expect(result.visibleMessages[0].messageId).toBe(102);
      expect(result.visibleMessages[1].messageId).toBe(103);
      expect(result.scrollTop).toBe(100);
    });

    test('includes buffer area', () => {
      const container = mockContainer(100, 400);
      const index = new Map([
        [101, { offset: 50, messageId: 101 }],   // In buffer above
        [102, { offset: 150, messageId: 102 }],  // In viewport
        [103, { offset: 550, messageId: 103 }]   // In buffer below
      ]);

      const result = getVisibleRange(container, index, 100); // 100px buffer

      expect(result.visibleMessages).toHaveLength(3);
    });

    test('handles null inputs', () => {
      expect(getVisibleRange(null, new Map())).toBeNull();
      expect(getVisibleRange(mockContainer(), null)).toBeNull();
    });
  });
});
