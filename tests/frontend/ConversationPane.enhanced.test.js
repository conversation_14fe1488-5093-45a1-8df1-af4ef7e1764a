/**
 * Enhanced tests for ConversationPane with message highlighting and lazy loading
 */

const React = require('react');
const { render, screen, fireEvent, waitFor, act } = require('@testing-library/react');
require('@testing-library/jest-dom');

const ConversationPane = require('../../src/frontend/components/ConversationPane.jsx').default;

// Mock the messageNavigator module
jest.mock('../../src/frontend/utils/messageNavigator.js', () => ({
  buildRealIndex: jest.fn(() => new Map([
    [101, { offset: 0, index: 0, messageId: 101, element: null }],
    [102, { offset: 100, index: 1, messageId: 102, element: null }],
    [103, { offset: 200, index: 2, messageId: 103, element: null }]
  ])),
  scrollTo: jest.fn(() => Promise.resolve(100)),
  clearHighlight: jest.fn(),
  buildLazyLoadIndex: jest.fn((messages, pageSize) => ({
    pages: [
      { startIndex: 0, endIndex: pageSize - 1, messages: messages.slice(0, pageSize) },
      { startIndex: pageSize, endIndex: messages.length - 1, messages: messages.slice(pageSize) }
    ],
    pageSize,
    totalMessages: messages.length,
    totalPages: Math.ceil(messages.length / pageSize)
  })),
  DEFAULT_PAGE_SIZE: 50,
  HIGHLIGHT_CLASS: 'message-highlighted'
}));

describe('ConversationPane Enhanced Features', () => {
  const mockMessages = [
    { message_id: 101, turn_id: 1, role: 'user', content: 'Hello' },
    { message_id: 102, turn_id: 1, role: 'assistant', content: 'Hi there!' },
    { message_id: 103, turn_id: 2, role: 'user', content: 'How are you?' }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    test('renders messages with data-message-id attributes', () => {
      render(React.createElement(ConversationPane, { messages: mockMessages }));

      expect(screen.getByTestId('message-101')).toBeInTheDocument();
      expect(screen.getByTestId('message-102')).toBeInTheDocument();
      expect(screen.getByTestId('message-103')).toBeInTheDocument();

      const message101 = screen.getByTestId('message-101');
      expect(message101).toHaveAttribute('data-message-id', '101');
    });

    test('renders empty state when no messages', () => {
      render(React.createElement(ConversationPane, { messages: [] }));
      expect(screen.getByText('暂无消息')).toBeInTheDocument();
    });
  });

  describe('Message Highlighting', () => {
    test('applies highlight class to highlighted message', () => {
      const { rerender } = render(React.createElement(ConversationPane, { 
        messages: mockMessages 
      }));

      // Initially no messages should be highlighted
      const message101 = screen.getByTestId('message-101');
      expect(message101).not.toHaveClass('message-highlighted');

      // This test simulates the highlighting behavior
      // In real usage, highlighting would be triggered by scrollToMessage
      const highlightedMessage = screen.getByTestId('message-101');
      expect(highlightedMessage).toBeInTheDocument();
    });
  });

  describe('Lazy Loading', () => {
    const createLargeMessageList = (count) => 
      Array.from({ length: count }, (_, i) => ({
        message_id: i + 1,
        turn_id: Math.floor(i / 2) + 1,
        role: i % 2 === 0 ? 'user' : 'assistant',
        content: `Message ${i + 1}`
      }));

    test('enables lazy loading for large message lists', async () => {
      const largeMessages = createLargeMessageList(100);
      
      render(React.createElement(ConversationPane, { 
        messages: largeMessages,
        enableLazyLoading: true,
        lazyLoadPageSize: 20
      }));

      // Should show loading indicator
      await waitFor(() => {
        expect(screen.getByText('加载更多消息...')).toBeInTheDocument();
        expect(screen.getByText(/已显示.*条消息/)).toBeInTheDocument();
      });
    });

    test('loads more messages on scroll', async () => {
      const largeMessages = createLargeMessageList(60);

      const { container } = render(React.createElement(ConversationPane, {
        messages: largeMessages,
        enableLazyLoading: true,
        lazyLoadPageSize: 20
      }));

      const messagesContainer = container.querySelector('.messages');

      if (messagesContainer) {
        // Simulate scroll to bottom
        Object.defineProperty(messagesContainer, 'scrollTop', { value: 800, writable: true });
        Object.defineProperty(messagesContainer, 'scrollHeight', { value: 1000, writable: true });
        Object.defineProperty(messagesContainer, 'clientHeight', { value: 400, writable: true });

        await act(async () => {
          fireEvent.scroll(messagesContainer);
        });

        // Should trigger lazy loading
        await waitFor(() => {
          expect(screen.getByText('加载更多消息...')).toBeInTheDocument();
        });
      }
    });

    test('disables lazy loading for small message lists', () => {
      render(React.createElement(ConversationPane, { 
        messages: mockMessages,
        enableLazyLoading: true,
        lazyLoadPageSize: 50
      }));

      // Should not show loading indicator for small lists
      expect(screen.queryByText('加载更多消息...')).not.toBeInTheDocument();
    });
  });

  describe('Scroll to Message', () => {
    test('exposes scroll function through onScrollToMessage callback', async () => {
      const scrollCallback = { current: null };

      render(React.createElement(ConversationPane, {
        messages: mockMessages,
        onScrollToMessage: scrollCallback
      }));

      // Wait for the scroll function to be exposed
      await waitFor(() => {
        expect(scrollCallback.current).toBeDefined();
        expect(typeof scrollCallback.current).toBe('function');
      }, { timeout: 2000 });
    });

    test('scroll helper button triggers scroll for testing', async () => {
      const scrollCallback = { current: null };
      
      render(React.createElement(ConversationPane, { 
        messages: mockMessages,
        onScrollToMessage: scrollCallback
      }));

      const scrollHelper = screen.getByTestId('scroll-helper');
      expect(scrollHelper).toBeInTheDocument();
      expect(scrollHelper).toHaveStyle('display: none'); // Hidden in production

      await act(async () => {
        fireEvent.click(scrollHelper);
      });

      // Should trigger scroll to first message
      const { scrollTo } = require('../../src/frontend/utils/messageNavigator.js');
      await waitFor(() => {
        expect(scrollTo).toHaveBeenCalled();
      });
    });
  });

  describe('Auto-scroll Behavior', () => {
    test('auto-scrolls to bottom by default', () => {
      const { container } = render(React.createElement(ConversationPane, { 
        messages: mockMessages 
      }));

      const messagesContainer = container.querySelector('.messages');
      expect(messagesContainer).toBeInTheDocument();
      // Auto-scroll behavior is tested through the useEffect
    });

    test('can disable auto-scroll to bottom', () => {
      render(React.createElement(ConversationPane, { 
        messages: mockMessages,
        autoScrollToBottom: false
      }));

      // Component should render without auto-scrolling
      expect(screen.getByTestId('message-101')).toBeInTheDocument();
    });
  });

  describe('Message Interaction', () => {
    test('clears highlight when clicking container', async () => {
      const { container } = render(React.createElement(ConversationPane, { 
        messages: mockMessages 
      }));

      const messagesContainer = container.querySelector('.messages');
      
      await act(async () => {
        fireEvent.click(messagesContainer);
      });

      const { clearHighlight } = require('../../src/frontend/utils/messageNavigator.js');
      expect(clearHighlight).toHaveBeenCalled();
    });
  });

  describe('Form Submission', () => {
    test('sends message and clears input', async () => {
      const onSend = jest.fn();
      
      render(React.createElement(ConversationPane, { 
        messages: mockMessages,
        onSend
      }));

      const input = screen.getByPlaceholderText('输入消息并回车发送');
      const submitButton = screen.getByText('发送');

      await act(async () => {
        fireEvent.change(input, { target: { value: 'Test message' } });
        fireEvent.click(submitButton);
      });

      expect(onSend).toHaveBeenCalledWith('Test message');
      expect(input.value).toBe('');
    });

    test('does not send empty messages', async () => {
      const onSend = jest.fn();
      
      render(React.createElement(ConversationPane, { 
        messages: mockMessages,
        onSend
      }));

      const input = screen.getByPlaceholderText('输入消息并回车发送');
      const submitButton = screen.getByText('发送');

      await act(async () => {
        fireEvent.change(input, { target: { value: '   ' } }); // Only whitespace
        fireEvent.click(submitButton);
      });

      expect(onSend).not.toHaveBeenCalled();
    });
  });

  describe('Performance and Edge Cases', () => {
    test('handles messages without message_id gracefully', () => {
      const messagesWithoutId = [
        { id: 201, role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi' } // No ID at all
      ];

      render(React.createElement(ConversationPane, { 
        messages: messagesWithoutId 
      }));

      expect(screen.getByText('Hello')).toBeInTheDocument();
      expect(screen.getByText('Hi')).toBeInTheDocument();
    });

    test('handles rapid message updates', async () => {
      const { rerender } = render(React.createElement(ConversationPane, { 
        messages: mockMessages 
      }));

      const newMessages = [...mockMessages, {
        message_id: 104,
        turn_id: 3,
        role: 'user',
        content: 'New message'
      }];

      await act(async () => {
        rerender(React.createElement(ConversationPane, { 
          messages: newMessages 
        }));
      });

      expect(screen.getByTestId('message-104')).toBeInTheDocument();
      expect(screen.getByText('New message')).toBeInTheDocument();
    });
  });
});
