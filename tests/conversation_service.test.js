const request = require('supertest');
const appModule = require('../src/index.js');
const app = appModule.default || appModule;

let server;

beforeAll(() => {
  // Start the app on an ephemeral port so supertest's agent can connect and we can close it later.
  server = app.listen(0);
});

afterAll((done) => {
  // Ensure the server is closed to avoid open handle warnings.
  if (server && server.close) {
    server.close(done);
  } else {
    done();
  }
});

describe('Conversation API', () => {
  test('POST /api/topics/:topic_id/messages persists a message', async () => {
    const res = await request(server)
      .post('/api/topics/123/messages')
      .send({ role: 'user', content: 'Hello' })
      .expect(201);
    expect(res.body).toHaveProperty('message_id');
    expect(res.body.topic_id).toBe(123);
    expect(res.body.role).toBe('user');
    expect(res.body.content).toBe('Hello');
  });
});