# Conversation & Summary Services - Developer Notes

This README documents how to run tests and start the minimal worker/components for the topic-persistent-learning feature in this repository.

快速开始（开发者）：
1. 安装开发依赖：
   - npm install

2. 运行测试套件：
   - npm test

3. 在本地启动 express 应用（用于手动测试）：
   - node src/index.js
   - 说明：`src/index.js` 导出的是 express 应用；在开发环境中建议用一个小的启动脚本将其监听到端口。

Key modules:
- src/index.js
  - Express routes for POST /api/topics/:topic_id/messages
  - POST /api/topics/:topic_id/summary/regenerate
  - GET /api/topics/:topic_id/conversation
  - GET /api/topics/:topic_id/summary

- src/services/conversation_service.js
  - produceMessage(...) — best-effort persistence to Manticore
  - enqueueRegenerateSummary(...) — fire-and-forget worker invocation

- src/workers/summary_worker.js
  - processSummaryTask(task, options) — generates structured summary, vectorizes (best-effort), writes back with optimistic retries
  - Emits telemetry events (via src/lib/telemetry.js) for write success/retry/failure and vectorize errors

- src/lib/telemetry.js
  - Lightweight, test-friendly telemetry hooks used across services.

Testing guidance:
- Unit tests use jest and mock external dependencies (manticore client, retrieval service, etc).
- E2E-style tests under tests/e2e/* mock external services but exercise the HTTP handlers and worker flow end-to-end in-process.
- To add frontend component tests, add React & testing library dependencies and replace placeholder tests in tests/frontend/.

Notes:
- This is an MVP implementation focused on backend behavior and TDD-friendly units. The telemetry implementation is intentionally minimal and should be replaced with an exporter to your production monitoring system when integrating.