/*
src/services/retrieval_service.js

Retrieval Service - hybrid fulltext + vector search helper

Exports:
- async function searchContext({ topic_id, query, top_k = 10, mode = 'hybrid' }, options = {})
  - options may include: vector (embedding array), knnField, filters, client (injected manticore client)

Behavior:
- If vector provided and no query -> knnSearch
- If query provided and no vector -> match search
- If both provided and mode === 'hybrid' -> knn first to get candidate ids, then match constrained by ids
- Returns standardized result: { results: [{ id, score, source_type, snippet }], meta: { knn, confidence } }
*/

const {
  knnSearch,
  hybridSearch // available from manticore client implementation
} = require('../lib/manticore_http_client');

function normalizeHits(manticoreHits) {
  // manticoreHits expected shape: { hits: { hits: [ { _id, _score, _source } ], total } }
  const hits = (manticoreHits && manticoreHits.hits && manticoreHits.hits.hits) || [];
  return hits.map(h => ({
    id: h._id,
    score: h._score || null,
    source_type: (h._source && h._source.source_type) || 'conversation',
    snippet: (h._source && h._source.content) || (h._source && h._source.chunk_text) || ''
  }));
}

/**
 * confidence heuristic: based on presence of scores and number of hits
 */
function computeConfidence(hits) {
  if (!hits || hits.length === 0) return 0.0;
  const scores = hits.map(h => h.score).filter(s => typeof s === 'number');
  if (scores.length === 0) return 0.5;
  const avg = scores.reduce((a, b) => a + b, 0) / scores.length;
  // normalize heuristically
  return Math.max(0, Math.min(1, (avg + 1) / 2));
}

async function searchContext({ topic_id, query = null, top_k = 10, mode = 'hybrid' } = {}, options = {}) {
  const client = options.client || { knnSearch, hybridSearch };
  const knnField = options.knnField || 'summary_vector';
  const vector = options.vector || null;
  const filters = options.filters || (topic_id ? { topic_id } : null);

  // If neither query nor vector provided, return empty result set (avoid calling remote search that may 500)
  if (!query && !vector) {
    return { results: [], meta: { knn: null, confidence: 0 } };
  }

  // Case 1: both provided and hybrid mode -> prefer knn-first constrained match
  if (query && vector && mode === 'hybrid') {
    // 1) knn to get candidate ids
    const knnRes = await client.knnSearch({ index: 'conversations_rt', field: knnField, vector, k: top_k, ef: options.ef || 64, filters });
    const knnHits = (knnRes && knnRes.hits && knnRes.hits.hits) || [];
    const ids = knnHits.map(h => h._id);
    if (ids.length === 0) {
      return { results: [], meta: { knn: knnRes, confidence: 0 } };
    }
    // 2) constrained match by ids - reuse hybridSearch or search with filter
    const matchRes = await client.hybridSearch({ index: 'conversations_rt', matchQuery: query, knnField, knnVector: vector, k: top_k });
    // hybridSearch in our client returns { hits: <res>, knn: <knnRes> }
    const hits = matchRes && matchRes.hits ? matchRes.hits : matchRes;
    const results = normalizeHits(hits);
    const confidence = computeConfidence(results);
    return { results, meta: { knn: knnRes, confidence } };
  }

  // Case 2: only vector provided -> knn search
  if (vector && !query) {
    const knnRes = await client.knnSearch({ index: 'conversations_rt', field: knnField, vector, k: top_k, ef: options.ef || 64, filters });
    const results = normalizeHits(knnRes);
    const confidence = computeConfidence(results);
    return { results, meta: { knn: knnRes, confidence } };
  }

  // Case 3: only query provided -> match search
  if (query && !vector) {
    const matchRes = await client.hybridSearch({ index: 'conversations_rt', matchQuery: query, k: top_k });
    const hits = matchRes && matchRes.hits ? matchRes.hits : matchRes;
    const results = normalizeHits(hits);
    const confidence = computeConfidence(results);
    return { results, meta: { knn: null, confidence } };
  }

  throw new Error('searchContext requires at least query or vector');
}

module.exports = {
  searchContext,
  normalizeHits,
  computeConfidence
};