import React from 'react';

export default function SummaryPane({ summaries = [], onSelect = () => {} }) {
  return (
    <div className="summary-pane" style={{ padding: 12, borderLeft: '1px solid #eee', height: '100%', overflow: 'auto' }}>
      <div style={{ marginBottom: 8, fontWeight: 'bold' }}>摘要</div>
      {summaries.length === 0 ? (
        <div style={{ color: '#666' }}>暂无摘要</div>
      ) : (
        summaries.map(s => (
          <div
            key={s.summary_id || s.id || Math.random()}
            data-testid={`summary-${s.id || s.summary_id}`}
            onClick={() => onSelect(s)}
            style={{
              cursor: 'pointer',
              padding: 8,
              borderRadius: 6,
              background: '#fafafa',
              marginBottom: 8,
              border: '1px solid #f0f0f0'
            }}
          >
            <div style={{ fontSize: 12, color: '#888' }}>{s.label || s.title || `摘要 ${s.summary_id || s.id || ''}`}</div>
            <div style={{ marginTop: 6, whiteSpace: 'pre-wrap' }}>{s.text || s.summary || ''}</div>
          </div>
        ))
      )}
    </div>
  );
}