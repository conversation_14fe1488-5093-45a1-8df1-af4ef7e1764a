import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';

// Import messageNavigator utilities
const {
  buildRealIndex,
  scrollTo,
  clearHighlight,
  buildLazyLoadIndex,
  DEFAULT_PAGE_SIZE,
  HIGHLIGHT_CLASS
} = require('../utils/messageNavigator.js');

export default function ConversationPane({
  messages = [],
  onSend = () => {},
  onScrollToMessage = null,
  enableLazyLoading = false,
  lazyLoadPageSize = DEFAULT_PAGE_SIZE,
  autoScrollToBottom = true
}) {
  const [text, setText] = useState('');
  const [visibleMessages, setVisibleMessages] = useState(messages);
  const [currentPage, setCurrentPage] = useState(0);
  const [highlightedMessageId, setHighlightedMessageId] = useState(null);

  const containerRef = useRef(null);
  const messageIndexRef = useRef(new Map());
  const lazyLoadIndexRef = useRef(null);

  // Build lazy loading index when messages change
  const lazyLoadIndex = useMemo(() => {
    if (!enableLazyLoading || messages.length <= lazyLoadPageSize) {
      return null;
    }
    return buildLazyLoadIndex(messages, lazyLoadPageSize);
  }, [messages, enableLazyLoading, lazyLoadPageSize]);

  // Update lazy load index ref
  useEffect(() => {
    lazyLoadIndexRef.current = lazyLoadIndex;
  }, [lazyLoadIndex]);

  // Initialize visible messages for lazy loading
  useEffect(() => {
    if (enableLazyLoading && lazyLoadIndex) {
      // Load first few pages initially
      const initialPages = Math.min(2, lazyLoadIndex.totalPages);
      const initialMessages = [];
      for (let i = 0; i < initialPages; i++) {
        initialMessages.push(...lazyLoadIndex.pages[i].messages);
      }
      setVisibleMessages(initialMessages);
      setCurrentPage(initialPages - 1);
    } else {
      setVisibleMessages(messages);
    }
  }, [messages, enableLazyLoading, lazyLoadIndex]);

  // Build message index when visible messages change
  useEffect(() => {
    if (containerRef.current && visibleMessages.length > 0) {
      // Use a timeout to ensure DOM is updated
      setTimeout(() => {
        messageIndexRef.current = buildRealIndex(containerRef.current, visibleMessages);
      }, 100);
    }
  }, [visibleMessages]);

  // Auto-scroll to bottom when messages change (if enabled)
  useEffect(() => {
    if (autoScrollToBottom && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [messages, autoScrollToBottom]);

  // Scroll to message function
  const scrollToMessage = useCallback(async (messageId, options = {}) => {
    if (!containerRef.current || !messageIndexRef.current) return null;

    // If lazy loading is enabled, ensure the message is loaded
    if (enableLazyLoading && lazyLoadIndexRef.current) {
      const messageIndex = messages.findIndex(m => (m.message_id || m.id) === messageId);
      if (messageIndex !== -1) {
        const targetPage = Math.floor(messageIndex / lazyLoadPageSize);
        if (targetPage > currentPage) {
          // Load more pages up to the target
          const newMessages = [];
          for (let i = 0; i <= targetPage; i++) {
            if (i < lazyLoadIndexRef.current.pages.length) {
              newMessages.push(...lazyLoadIndexRef.current.pages[i].messages);
            }
          }
          setVisibleMessages(newMessages);
          setCurrentPage(targetPage);

          // Wait for DOM update before scrolling
          setTimeout(() => {
            messageIndexRef.current = buildRealIndex(containerRef.current, newMessages);
            scrollTo(containerRef.current, messageId, messageIndexRef.current, options);
          }, 100);
          return;
        }
      }
    }

    return scrollTo(containerRef.current, messageId, messageIndexRef.current, options);
  }, [enableLazyLoading, currentPage, lazyLoadPageSize, messages]);

  // Expose scroll function to parent component
  useEffect(() => {
    if (onScrollToMessage) {
      if (typeof onScrollToMessage === 'object' && onScrollToMessage.current !== undefined) {
        // Ref-based callback (new enhanced mode)
        onScrollToMessage.current = scrollToMessage;
      }
      // For backward compatibility, we also support direct function calls in the scroll helper
    }
  }, [onScrollToMessage, scrollToMessage]);

  // Handle lazy loading on scroll
  const handleScroll = useCallback(() => {
    if (!enableLazyLoading || !lazyLoadIndexRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // Load more when near bottom
    if (scrollTop + clientHeight >= scrollHeight - 200) {
      const nextPage = currentPage + 1;
      if (nextPage < lazyLoadIndexRef.current.totalPages &&
          lazyLoadIndexRef.current.pages[nextPage] &&
          lazyLoadIndexRef.current.pages[nextPage].messages) {
        const newMessages = [...visibleMessages, ...lazyLoadIndexRef.current.pages[nextPage].messages];
        setVisibleMessages(newMessages);
        setCurrentPage(nextPage);
      }
    }
  }, [enableLazyLoading, currentPage, visibleMessages]);

  function handleSubmit(e) {
    e.preventDefault();
    const trimmed = text.trim();
    if (!trimmed) return;
    onSend(trimmed);
    setText('');
  }

  // Clear highlight when clicking elsewhere
  const handleContainerClick = useCallback((e) => {
    if (e.target === containerRef.current) {
      clearHighlight(containerRef.current);
      setHighlightedMessageId(null);
    }
  }, []);

  return (
    <div className="conversation-pane" style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <div
        ref={containerRef}
        className="messages"
        style={{ flex: 1, overflow: 'auto', padding: 12 }}
        onScroll={handleScroll}
        onClick={handleContainerClick}
      >
        {visibleMessages.length === 0 ? (
          <div style={{ color: '#666' }}>暂无消息</div>
        ) : (
          visibleMessages.map(m => {
            const messageId = m.message_id || m.id;
            return (
              <div
                key={messageId || Math.random()}
                data-message-id={messageId}
                data-testid={`message-${messageId}`}
                className={`message ${highlightedMessageId === messageId ? HIGHLIGHT_CLASS : ''}`}
                style={{
                  marginBottom: 8,
                  transition: 'background-color 0.3s ease',
                  padding: 4,
                  borderRadius: 4
                }}
              >
                <div style={{ fontSize: 12, color: '#888' }}>{m.sender || m.role || 'user'}</div>
                <div style={{
                  background: '#f5f5f5',
                  padding: 8,
                  borderRadius: 6,
                  whiteSpace: 'pre-wrap'
                }}>
                  {m.content}
                </div>
              </div>
            );
          })
        )}

        {/* Loading indicator for lazy loading */}
        {enableLazyLoading && lazyLoadIndexRef.current && currentPage < lazyLoadIndexRef.current.totalPages - 1 && (
          <div style={{ textAlign: 'center', padding: 16, color: '#666' }}>
            <div>加载更多消息...</div>
            <div style={{ fontSize: 12, marginTop: 4 }}>
              已显示 {visibleMessages.length} / {messages.length} 条消息
            </div>
          </div>
        )}

        {/* Test helper button (hidden in production) */}
        {onScrollToMessage && (
          <button
            data-testid="scroll-helper"
            style={{ display: 'none' }}
            onClick={() => {
              const firstMessage = visibleMessages[0];
              if (firstMessage) {
                const messageId = firstMessage.message_id || firstMessage.id;

                // Support both function and ref-based callbacks for backward compatibility
                if (typeof onScrollToMessage === 'function') {
                  onScrollToMessage(messageId);
                } else if (typeof onScrollToMessage === 'object' && onScrollToMessage.current) {
                  onScrollToMessage.current(messageId);
                } else {
                  scrollToMessage(messageId);
                }
              }
            }}
          >
            Test Scroll Helper
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} style={{ borderTop: '1px solid #eee', padding: 8, display: 'flex' }}>
        <input
          value={text}
          onChange={e => setText(e.target.value)}
          placeholder="输入消息并回车发送"
          style={{ flex: 1, padding: 8, borderRadius: 4, border: '1px solid #ddd' }}
        />
        <button type="submit" style={{ marginLeft: 8 }}>
          发送
        </button>
      </form>


    </div>
  );
}