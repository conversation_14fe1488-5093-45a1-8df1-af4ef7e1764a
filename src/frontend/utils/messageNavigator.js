/**
 * messageNavigator
 *
 * Enhanced utility to map message_id/turn_id to DOM offsets and provide
 * helpers for "scroll to message" logic, message highlighting, and lazy loading
 * used by frontend components.
 *
 * Features:
 * - buildIndex(messages): builds a lookup from message_id -> offsetTop
 * - findOffset(messageId): returns recorded offset (or null)
 * - scrollTo(container, messageId): performs smooth scroll to the element
 * - highlightMessage(container, messageId): highlights a specific message
 * - clearHighlight(container): removes all message highlights
 * - buildLazyLoadIndex(messages, pageSize): creates pagination index for lazy loading
 * - getVisibleRange(container, indexMap): determines which messages are currently visible
 *
 * Note: In a real app the offsets would be read from actual rendered elements.
 * For testing we can simulate by attaching data-offset attributes or mocking getBoundingClientRect.
 */

// Constants for lazy loading and highlighting
const DEFAULT_PAGE_SIZE = 50;
const HIGHLIGHT_CLASS = 'message-highlighted';
const HIGHLIGHT_DURATION = 3000; // ms

function buildIndex(messages = []) {
  // messages: Array<{ message_id, turn_id }>
  const index = new Map();
  // For demo/test purposes we compute a synthetic offset (e.g., message index * 100)
  // In real implementation, this would read actual DOM element positions
  for (let i = 0; i < messages.length; i++) {
    const m = messages[i];
    const messageId = m.message_id || m.id;
    if (messageId) {
      index.set(messageId, {
        offset: i * 100, // synthetic offset for testing
        index: i,
        turnId: m.turn_id,
        messageId: messageId
      });
    }
  }
  return index;
}

function buildRealIndex(container, messages = []) {
  // Build index from actual DOM elements
  const index = new Map();
  if (!container) return index;

  messages.forEach((m, i) => {
    const messageId = m.message_id || m.id;
    if (messageId) {
      const element = container.querySelector(`[data-message-id="${messageId}"]`);
      if (element) {
        const rect = element.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        index.set(messageId, {
          offset: rect.top - containerRect.top + container.scrollTop,
          index: i,
          turnId: m.turn_id,
          messageId: messageId,
          element: element
        });
      }
    }
  });
  return index;
}

function findOffset(indexMap, messageId) {
  if (!indexMap || !indexMap.has(messageId)) return null;
  const entry = indexMap.get(messageId);
  return entry ? entry.offset : null;
}

function findMessageInfo(indexMap, messageId) {
  if (!indexMap || !indexMap.has(messageId)) return null;
  return indexMap.get(messageId);
}

async function scrollTo(container, messageId, indexMap, options = {}) {
  // container: DOM element (optional for tests) - if absent, function returns the offset
  const { highlight = true, behavior = 'smooth' } = options;
  const messageInfo = findMessageInfo(indexMap, messageId);

  if (!messageInfo) return null;

  if (container && typeof container.scrollTo === 'function') {
    try {
      container.scrollTo({ top: messageInfo.offset, behavior });

      // Highlight the message after scrolling
      if (highlight) {
        setTimeout(() => highlightMessage(container, messageId, indexMap), 100);
      }

      return messageInfo.offset;
    } catch (err) {
      // fallback synchronous scroll
      container.scrollTop = messageInfo.offset;
      if (highlight) {
        highlightMessage(container, messageId, indexMap);
      }
      return messageInfo.offset;
    }
  }

  // If no container provided, return offset for tests to assert
  return messageInfo.offset;
}

function highlightMessage(container, messageId, indexMap) {
  if (!container) return false;

  // Clear existing highlights
  clearHighlight(container);

  // Find and highlight the target message
  const messageInfo = findMessageInfo(indexMap, messageId);
  if (!messageInfo) return false;

  let targetElement = messageInfo.element;
  if (!targetElement) {
    targetElement = container.querySelector(`[data-message-id="${messageId}"]`);
  }

  if (targetElement) {
    targetElement.classList.add(HIGHLIGHT_CLASS);

    // Auto-remove highlight after duration
    setTimeout(() => {
      targetElement.classList.remove(HIGHLIGHT_CLASS);
    }, HIGHLIGHT_DURATION);

    return true;
  }

  return false;
}

function clearHighlight(container) {
  if (!container) return;

  const highlighted = container.querySelectorAll(`.${HIGHLIGHT_CLASS}`);
  highlighted.forEach(el => el.classList.remove(HIGHLIGHT_CLASS));
}

// Lazy loading utilities
function buildLazyLoadIndex(messages = [], pageSize = DEFAULT_PAGE_SIZE) {
  const pages = [];
  for (let i = 0; i < messages.length; i += pageSize) {
    pages.push({
      startIndex: i,
      endIndex: Math.min(i + pageSize - 1, messages.length - 1),
      messages: messages.slice(i, i + pageSize)
    });
  }
  return {
    pages,
    pageSize,
    totalMessages: messages.length,
    totalPages: pages.length
  };
}

function getVisibleRange(container, indexMap, buffer = 200) {
  if (!container || !indexMap) return null;

  const containerRect = container.getBoundingClientRect();
  const scrollTop = container.scrollTop;
  const viewportTop = scrollTop - buffer;
  const viewportBottom = scrollTop + containerRect.height + buffer;

  const visibleMessages = [];

  for (const [messageId, info] of indexMap) {
    if (info.offset >= viewportTop && info.offset <= viewportBottom) {
      visibleMessages.push({
        messageId,
        ...info
      });
    }
  }

  return {
    visibleMessages,
    viewportTop,
    viewportBottom,
    scrollTop
  };
}

module.exports = {
  buildIndex,
  buildRealIndex,
  findOffset,
  findMessageInfo,
  scrollTo,
  highlightMessage,
  clearHighlight,
  buildLazyLoadIndex,
  getVisibleRange,
  DEFAULT_PAGE_SIZE,
  HIGHLIGHT_CLASS,
  HIGHLIGHT_DURATION
};