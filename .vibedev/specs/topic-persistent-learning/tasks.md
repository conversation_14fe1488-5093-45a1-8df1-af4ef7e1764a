# Implementation Tasks — topic-persistent-learning

1. [x] 1.0 Initialize feature module and tests
- Objective: Create the feature directory, baseline test harness and CI-friendly scripts to run tests.
- Files/Components: create `.vibedev/specs/topic-persistent-learning/` support files, add test runner config (jest/mocha) and package scripts.
- References: requirements 2.1, 2.6 (ensure testability and observability).

1. [x] 1.1 Adopt Manticore as primary storage & retrieval
- Objective: Treat Manticore RT indexes as the single source-of-truth for messages, summaries and document chunks; add integration scaffolding and examples.
- Files/Components to add/modify:
  - `.vibedev/specs/topic-persistent-learning/design.md` (storage & retrieval architecture section) — updated.
  - `.vibedev/specs/topic-persistent-learning/tasks.md` (this file) — add detailed implementation tasks.
  - `examples/manticore/` (CREATE RT table snippets, docker-compose) — to create.
  - `src/lib/manticore_http_client.js` — new Node.js HTTP client wrapper for Manticore. (implemented: CommonJS client + unit tests)
  - `src/services/conversation_service.*` — backend service adapters.
  - `src/workers/summary_worker.*` — worker implementation.
  - `tests/integration/manticore_integration.test.js` — integration tests.
- Priority: high
- Estimated effort: 4-8 hours for scaffolding + examples
- Acceptance criteria:
  - design.md contains "存储与检索架构" section stating Manticore is primary storage (done).
  - tasks.md lists concrete tasks and patches for client code, docker-compose and SQL examples.
  - Example client file path and docker-compose path are provided and reviewed.

1. [x] 1.2 Create Node.js Manticore HTTP client wrapper (TDD)
- Objective: Implement `src/lib/manticore_http_client.js` that encapsulates HTTP JSON API calls (insert, bulk, search, knn).
- Files/Components:
  - `src/lib/manticore_http_client.js` — implemented
  - `tests/manticore/manticore_http_client.test.js` — unit tests (mock axios) added
- Steps performed:
  1. Created client wrapper with env-configurable host/port/timeouts.
  2. Implemented single upsert, bulk upsert, knnSearch and combined knn+match helpers.
  3. Added unit tests that mock axios and assert payload shapes.
- Priority: high
- Est: 4h
- Acceptance:
  - Unit tests pass locally (mocked).
  - Example usage exists in tasks documentation.

1. [x] 1.3 Add docker-compose for local Manticore + mock LLM (for CI)
- Objective: Provide reproducible local dev and CI environment.
- Files/Components:
  - `examples/docker-compose.manticore.yml` — new file (implemented)
  - `examples/mock-llm/server.js` — new mock LLM service (implemented)
  - `examples/mock-llm/package.json` — package for mock service (implemented)
  - `examples/manticore/` — init/example config
- Steps:
  1. Add minimal docker-compose with `manticoresearch/manticore` image (ports 9306/9308) and a simple mock-llm service (HTTP server returning canned summaries).
  2. Document how to start/stop and run integration tests against it.
- Priority: high
- Est: 2h
- Acceptance:
  - `docker-compose -f examples/docker-compose.manticore.yml up -d` brings up Manticore and mock LLM.
  - Integration tests can run against the compose stack.

1. [x] 1.4 Implement Conversation Service persistence to Manticore (TDD)
- Objective: Wire `src/services/conversation_service.*` to persist incoming messages into Manticore using the client wrapper.
- Files/Components:
  - `src/services/conversation_service.js` (implemented: produceMessage with best-effort persistence)
  - `tests/integration/conversation_persistence.test.js`
- Steps:
  1. Implement `produceMessage(topic_id, user_message)` to format doc and call upsert.
  2. Ensure turn_id assignment and message_id idempotency (client-provided or generated).
  3. Add unit tests for formatting and mock integration tests against docker-compose.
- Priority: high
- Est: 8h
- Acceptance:
  - Integration test posts a message and reads it back via search API.

1. [x] 1.5 Implement Summary Worker & versioned writeback (TDD)
- Objective: Worker consumes summary tasks, writes summary JSON + summary_vector into the correct conversation record and increments `summary_version` with optimistic locking.
- Files/Components:
  - `src/workers/summary_worker.js` — implemented
  - `tests/summary_worker.test.js` — tests added
- Steps performed:
  1. Implemented generateSummary stub and vectorize stub for testability.
  2. Worker writes `summary` and `summary_vector` using REPLACE semantics and performs optimistic-retry behavior.
  3. Unit tests added to cover success, retry-on-transient-failure, and exhaustion cases.
- Priority: high
- Est: 8-12h
- Acceptance:
  - Worker writes summary and increments version; conflict triggers retry and logs degrade events (behavior covered by unit tests).

1. [x] 1.6 Implement Vectorizer worker + indexer integration (TDD)
- Objective: Implement vectorization pipeline that converts text to vectors and indexes into Manticore.
- Files/Components:
  - `src/workers/vectorizer.js` — implemented (existing)
  - `src/lib/manticore_http_client.js` — used by vectorizer
  - `tests/vectorizer.test.js` — unit tests added
- Steps performed:
  1. Verified `vectorizeText` and `vectorizeAndIndex` exist in `src/workers/vectorizer.js`.
  2. Added tests that mock OpenAI client and `upsertDocument` to assert index calls and returned vector.
- Priority: high
- Est: 6-8h
- Acceptance:
  - `vectorizeAndIndex` returns vector and upsert response; tests validate payload shape and call.

1. [x] 1.7 Implement Retrieval Service: hybrid fulltext+vector (TDD)
- Objective: Implement `searchContext(topic_id, query, top_k, mode)` that uses fulltext + vector queries against Manticore; include unit tests for ranking logic.
- Files/Components:
  - `src/services/retrieval_service.js` — implemented
  - `tests/retrieval_service.test.js` — tests added
- Steps performed:
  1. Implemented `searchContext` with knn-first hybrid strategy and normalization helpers.
  2. Added confidence heuristic and result normalization.
  3. Unit tests cover match-only, knn-only and hybrid knn->match flows.
- Priority: high
- Est: 6h
- Acceptance:
  - Tests pass (mocked); retrieval returns expected candidate ids for known dataset when integrated.

1. [x] 1.8 Conversation Prompt builder (with retrieval integration) + tests
- Objective: Implement prompt assembly that pulls short-term messages, long-term anchors from Retrieval Service and document chunks.
- Files/Components:
  - `src/lib/prompt_builder.js` — implemented
  - `tests/prompt_builder.test.js` — tests added
- Steps performed:
  1. Implemented prompt builder to include persona, recent N messages, top_k anchors, and basic chunk inclusion.
  2. Designed truncation heuristics and estimated token calculation.
  3. Added unit tests that mock retrieval client and validate prompt contents and truncation behavior.
- Priority: medium
- Est: 6h
- Acceptance:
  - Prompt builder includes fixed persona, recent N messages, top_k long-term anchors and chunks; returns structured promptText and metadata; tests validate behavior.

1. [x] 1.9 Summary regeneration API + tests (implemented)
- Objective: Implement endpoint to trigger summary regenerate for a given turn, with versioning and authorization checks.
- Files/Components:
  - `POST /api/topics/{topic_id}/summary/regenerate` handler
  - `tests/regenerate_api.test.js`
- Steps performed:
  1. Implemented endpoint in [`src/index.js`](src/index.js:29) to compute a placeholder new summary_version, enqueue a background regenerate task via [`enqueueRegenerateSummary`](src/services/conversation_service.js:41), and respond with 202 Accepted.
  2. Added/verified unit tests in [`tests/regenerate_api.test.js`](tests/regenerate_api.test.js:1) that assert successful enqueue and resilient behavior when enqueue fails.
- Priority: medium
- Est: 4h
- Acceptance:
  - regenerate endpoint enqueues regenerate task and returns new summary_version placeholder.
  - Unit tests pass (verified via `npm test`) — see test results.

1. [x] 1.10 Frontend endpoints and integration stubs (backend APIs) (implemented)
- Objective: Implement backend APIs consumed by frontend: GET conversation pages, GET summary, POST messages, POST regenerate.
- Files/Components implemented:
  - API route handlers in [`src/index.js:9`](src/index.js:9), [`src/index.js:29`](src/index.js:29), [`src/index.js:57`](src/index.js:57), [`src/index.js:71`](src/index.js:71)
  - Integration tests: existing tests exercising endpoints (e.g. [`tests/api_endpoints.test.js:1`](tests/api_endpoints.test.js:1), [`tests/regenerate_api.test.js:24`](tests/regenerate_api.test.js:24))
- Steps performed:
  1. Verified `POST /api/topics/:topic_id/messages` implemented in [`src/index.js:9`](src/index.js:9) and delegates to [`src/services/conversation_service.js:9`](src/services/conversation_service.js:9).
  2. Verified `POST /api/topics/:topic_id/summary/regenerate` implemented in [`src/index.js:29`](src/index.js:29) and delegates to [`src/services/conversation_service.js:41`](src/services/conversation_service.js:41).
  3. Verified `GET /api/topics/:topic_id/conversation` and `GET /api/topics/:topic_id/summary` implemented in [`src/index.js:57`](src/index.js:57) and [`src/index.js:71`](src/index.js:71) respectively, using `searchContext` from [`src/services/retrieval_service.js`](src/services/retrieval_service.js:1).
  4. Ran test suite to validate integration behavior.
- Priority: medium
- Est: 6h
- Acceptance:
  - endpoints return well-formed JSON matching frontend expectations.
  - Integration tests pass (verified via `npm test`).

1. [x] 1.11 Frontend: minimal Dual-Pane UI components + unit tests
 - Objective: Implement minimal React components to render conversation and summary panes, clickable summary items that call backend to fetch and highlight messages.
 - Files/Components:
   - `src/frontend/components/ConversationPane.*`
   - `src/frontend/components/SummaryPane.*`
 - Priority: low
 - Est: 8h
 - Acceptance:
   - clicking a summary item calls API to get message offset and triggers in-page scroll/highlight (mocked in tests).

1. [ ] 1.12 Message-highlighting and lazy-loading support (frontend tests)
- Objective: Implement mapping by message_id/turn_id to allow smooth scroll and highlight; implement lazy-loading for large conversations.
- Files/Components:
  - `src/frontend/utils/messageNavigator.*`
- Priority: low
- Est: 6h
- Acceptance:
  - component test demonstrates scroll to specific message_id with mocked large dataset.

1. [ ] 1.13 Telemetry hooks and tests
- Objective: Instrument retrieval latency, summary generation time, error rates with testable telemetry hooks.
- Files/Components:
  - `src/lib/telemetry.*`
- Priority: low
- Est: 4h
- Acceptance:
  - tests assert telemetry events emitted with expected fields.

1. [ ] 1.14 Error handling & degrade-mode tests
- Objective: Implement degrade mode fallbacks and unit tests verifying fallback behavior.
- Files/Components:
  - service-level fallback logic, tests simulating index/vector failures.
- Priority: medium
- Est: 4h
- Acceptance:
  - when vector/index fails, summary stored without vector and system logs degrade event.

1. [ ] 1.15 End-to-end automated integration test
- Objective: Create an automated test that posts messages, enqueues and runs the summary worker (mocked LLM/vectorizer), verifies summary stored and GET /summary returns clickable items metadata.
- Files/Components:
  - `tests/e2e/topic_persistent_e2e.test.*`
- Priority: medium
- Est: 8-12h
- Acceptance:
  - end-to-end test passes in CI emulation and validates core flow.

1. [ ] 1.16 Documentation for developers (code-centric, inline)
- Objective: Add README and inline comments for implemented modules to guide coding agents; keep concise and code-focused.
- Files/Components:
  - `src/services/README.md`
- Priority: low
- Est: 2-4h
- Acceptance:
  - README explains how to run tests and start workers locally for development.

1. [ ] 1.17 Finalize tests and mark feature-ready for implementation execution
- Objective: Ensure all tests pass locally and CI; prepare tasks for execution agents to pick up (no deployment).
- Priority: medium
- Est: 4h
- Acceptance:
  - all unit/integration/e2e tests included above pass in local test runner.

Do the tasks look good? If so, we can move on to finalizing the plan and calling the task-planning confirm step.

2. [ ] 2.0 Database schema and migration
- Objective: Implement DB migration scripts for tables `topics`, `documents/document_chunks`, `topic_document_links`, `conversations`.
- Files/Components: migrations (e.g., `migrations/001_create_topic_tables.sql`), ORM models or SQL schema files.
- Acceptance: matches schema described in design.md (conversations fields: message_id, turn_id, topic_id, content, summary, summary_vector, summary_version).
- References: requirements 2.1, 2.4; design Data Models.

3. [ ] 3.0 Conversation Service: message ingestion API + tests
- Objective: Implement backend endpoint to receive and persist messages; include turn_id assignment logic and unit tests.
- Files/Components: `src/services/conversation_service.ts` (or .py), `src/api/topics/[topic_id]/messages` handler, tests `tests/conversation_service.test.*`.
- Acceptance: POST /api/topics/{topic_id}/messages writes message with message_id, turn_id, created_at and returns persisted record.
- References: requirements 2.1 (F1.1, F1.4), 2.3 (F3.1), design Conversation Service.

4. [ ] 4.0 Turn completion logic and enqueue summary job
- Objective: Implement logic that marks a turn as complete and enqueues an asynchronous summary job.
- Files/Components: `conversation_service.completeTurn`, job enqueueing code, unit tests for enqueue behavior.
- Acceptance: completeTurn(turn_id) enqueues a summary task; unit test verifies enqueue call with correct metadata.
- References: requirements 2.3 (F3.1, F3.2), design Summary Service.

5. [ ] 5.0 Summary Worker: generate, store and version summaries (TDD)
- Objective: Implement worker that consumes turn tasks, calls LLM client (mocked in tests), stores `summary` JSON and `summary_version`, computes `summary_vector`.
- Files/Components: `src/workers/summary_worker.*`, `tests/summary_worker.test.*`, mock LLM/vectorizer.
- Acceptance: worker writes summary JSON into conversation row, increments summary_version and stores summary_vector (or null on vector error).
- References: requirements 2.2 (F3.2), 2.6, design Summary Service.

6. [ ] 6.0 Vectorizer worker + indexer integration (TDD)
- Objective: Implement vectorization pipeline that converts text to vectors and indexes into Manticore (or mock).
- Files/Components: `src/workers/vectorizer.*`, Manticore client wrapper `src/lib/manticore_client.*`, tests mocking vector model and index calls.
- Acceptance: vectorizeAndIndex(text, metadata) returns vector_id and stores vector in index with metadata (topic_id/doc_id/chunk_id).
- References: requirements 2.3 (F4.1), design Retrieval & Worker.

7. [ ] 7.0 Retrieval Service: hybrid search API + tests
- Objective: Implement `searchContext(topic_id, query, top_k, mode)` that uses fulltext + vector queries against Manticore; include unit tests for ranking logic (mocked index).
- Files/Components: `src/services/retrieval_service.*`, `tests/retrieval_service.test.*`.
- Acceptance: API returns ordered results with score and snippet; supports mode switching and returns confidence.
- References: requirements 2.3 (F4), design Retrieval Service.

8. [ ] 8.0 Conversation Prompt builder (with retrieval integration) + tests
- Objective: Implement prompt assembly that pulls short-term messages, long-term anchors from Retrieval Service and document chunks, and returns final prompt payload.
- Files/Components: `src/lib/prompt_builder.*`, tests validating included sections and size constraints.
- Acceptance: Prompt builder includes fixed persona, recent N messages, top_k long-term anchors and chunks; tests assert expected structure.
- References: requirements 2.3 (F4.2), design Conversation Service.

9. [ ] 9.0 Summary regeneration API + tests
- Objective: Implement endpoint to trigger summary regenerate for a given turn, with versioning and authorization checks.
- Files/Components: `POST /api/topics/{topic_id}/summary/regenerate`, backend handler, tests covering version increment and error paths.
- Acceptance: regenerate endpoint enqueues regenerate task and returns new summary_version placeholder.
- References: requirements 2.2 (summary re-generate), design Summary Service.

10. [ ] 10.0 Frontend endpoints and integration stubs (backend APIs)
- Objective: Implement backend APIs consumed by frontend: GET conversation pages, GET summary, POST messages, POST regenerate.
- Files/Components: API route handlers (as in design), integration tests hitting handler layer.
- Acceptance: endpoints return well-formed JSON matching frontend expectations.
- References: requirements 2.1 (F1.3, F1.4), 2.2 (F3.4), design Frontend Interfaces.

11. [ ] 11.0 Frontend: minimal Dual-Pane UI components + unit tests
- Objective: Implement minimal React/Vue components to render left conversation pane and right summary pane, clickable summary items that call backend to fetch and highlight messages.
- Files/Components: `src/frontend/components/ConversationPane.*`, `SummaryPane.*`, unit tests for component rendering and click behavior (Jest/RTL or equivalent).
- Acceptance: clicking a summary item calls API to get message offset and triggers in-page scroll/highlight (mocked in tests).
- References: requirements 2.2 (F3.4), 2.5 (F2.1, F2.2), design Frontend.

12. [ ] 12.0 Message-highlighting and lazy-loading support (frontend tests)
- Objective: Implement mapping by message_id/turn_id to allow smooth scroll and highlight; implement lazy-loading for large conversations.
- Files/Components: frontend utilities `messageNavigator.*`, tests verifying lazy load triggers and highlight behavior.
- Acceptance: component test demonstrates scroll to specific message_id with mocked large dataset.
- References: requirements 2.5, design UI smooth scroll.

13. [ ] 13.0 Telemetry hooks and tests
- Objective: Instrument retrieval latency, summary generation time, error rates with testable telemetry hooks (no external monitoring required).
- Files/Components: `src/lib/telemetry.*`, unit tests asserting metrics emitted for mocked flows.
- Acceptance: tests assert telemetry events emitted with expected fields (topic_id, session_id, latency_ms).
- References: requirements 2.6, testing strategy.

14. [ ] 14.0 Error handling & degrade-mode tests
- Objective: Implement degrade mode fallbacks (e.g., skip vectors if indexing fails) and unit tests verifying fallback behavior.
- Files/Components: service-level fallback logic, tests simulating index/vector failures and ensuring conversation continues.
- Acceptance: when vector/index fails, summary stored without vector and system logs degrade event.
- References: requirements 3.1, design Error Handling.

15. [ ] 15.0 End-to-end automated integration test
- Objective: Create an automated test that posts messages, enqueues and runs the summary worker (mocked LLM/vectorizer), verifies summary stored and GET /summary returns clickable items metadata.
- Files/Components: `tests/e2e/topic_persistent_e2e.test.*` using in-memory DB or test fixtures.
- Acceptance: end-to-end test passes in CI emulation and validates core flow.
- References: requirements 2.1, 2.2, 2.3, testing strategy.

16. [ ] 16.0 Documentation for developers (code-centric, inline)
- Objective: Add README and inline comments for implemented modules to guide coding agents; keep concise and code-focused.
- Files/Components: `src/services/README.md`, inline code comments.
- Acceptance: README explains how to run tests and start workers locally for development.
- References: implementation / TDD flow expectations.

17. [ ] 17.0 Finalize tests and mark feature-ready for implementation execution
- Objective: Ensure all tests pass locally and CI; prepare tasks for execution agents to pick up (no deployment).
- Files/Components: test run script results, consolidated test reports.
- Acceptance: all unit/integration/e2e tests included above pass in local test runner.
- References: requirements 2.6, testing strategy.

Do the tasks look good? If so, we can move on to finalizing the plan and calling the task-planning confirm step.