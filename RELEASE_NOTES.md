# Release Notes — topic-persistent-learning (MVP)

Summary:
- Implemented topic-persistent learning feature: conversation persistence, hybrid retrieval, async summary generation, interactive script-style summaries with click-through to messages.
- Added telemetry hooks and optional OpenTelemetry OTLP integration.
- Included unit, integration, and end-to-end tests; CI workflow template added.

Key files:
- Backend routes & services: `src/index.js`, `src/services/conversation_service.js`
- Manticore client: `src/lib/manticore_http_client.js`
- Summary worker & vectorizer: `src/workers/summary_worker.js`, `src/workers/vectorizer.js`
- Retrieval & prompt builder: `src/services/retrieval_service.js`, `src/lib/prompt_builder.js`
- Telemetry: `src/lib/telemetry.js`, `src/lib/opentelemetry.js`
- Frontend minimal components: `src/frontend/components/ConversationPane.jsx`, `src/frontend/components/SummaryPane.jsx`
- Tests: unit/integration/e2e under `tests/` (e.g., `tests/e2e/topic_persistent_e2e.test.js`)
- CI: `.github/workflows/ci.yml`
- OTLP collector example: `examples/otel-collector/otel-collector-config.yml`
- Developer docs: `src/services/README.md`

如何运行测试：
1. 运行依赖安装：npm ci
2. 运行测试套件：npm test

本地运行（开发环境）：
1. 若需要，先通过 Docker 启动 Manticore，或使用示例 Compose 文件：`examples/docker-compose.manticore.yml`。
2. 启动应用：`node src/index.js`（此文件导出一个 express 应用；在开发时可用一个小的启动脚本监听端口）。
3. 可选：如果需要启用 OpenTelemetry，将 `OTEL_EXPORTER_OTLP_ENDPOINT` 设置为你的采集器端点。

Notes:
- The repository includes a lightweight OpenTelemetry bootstrap. To enable real OTLP export in production, install and configure the OpenTelemetry SDK and exporters (example packages are referenced in `src/lib/opentelemetry.js`).
- Frontend includes minimal React components for demonstration; full UI integration is out of MVP scope.