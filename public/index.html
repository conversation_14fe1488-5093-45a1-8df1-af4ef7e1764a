<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Topic Persistent - Minimal UI</title>
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <style>
      body { font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>; margin: 0; padding: 0; }
      .app { max-width: 900px; margin: 24px auto; padding: 16px; }
      .row { display:flex; gap:12px; align-items:center; margin-bottom:12px; }
      textarea { width:100%; height:80px; font-size:14px; padding:8px; }
      input[type="number"] { width:120px; padding:6px; }
      button { padding:8px 12px; cursor:pointer; }
      .messages { border:1px solid #eee; padding:8px; min-height:200px; }
      .message { padding:8px; border-bottom:1px solid #f1f1f1; }
      .meta { color:#666; font-size:12px; }
      .notice { color:#0a66ff; font-size:13px; margin-bottom:8px; }
    </style>
  </head>
  <body>
    <div class="app">
      <h2>Topic Persistent - Minimal UI</h2>
      <div class="notice">This minimal UI talks to the local Express API endpoints provided by the project.</div>

      <div class="row">
        <label>Topic ID: <input id="topicId" type="number" value="1" /></label>
        <label>Role:
          <select id="role">
            <option value="user">user</option>
            <option value="assistant">assistant</option>
          </select>
        </label>
        <button id="refresh">Refresh Conversation</button>
      </div>

      <div>
        <textarea id="content" placeholder="Type a message..."></textarea>
      </div>
      <div class="row">
        <button id="send">Send Message (POST /api/topics/:topic_id/messages)</button>
        <button id="regen">Enqueue Regenerate Summary</button>
      </div>

      <h3>Conversation</h3>
      <div id="messages" class="messages"></div>
    </div>

    <script src="app.js"></script>
  </body>
</html>