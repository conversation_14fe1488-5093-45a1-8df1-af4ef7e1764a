{"name": "@opentelemetry/propagator-b3", "version": "2.0.1", "description": "OpenTelemetry B3 propagator provides context propagation for systems that are using the B3 header format", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc mocha test/**/*.test.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../scripts/peer-api-check.js", "align-api-deps": "node ../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "monitoring", "b3"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": "^18.19.0 || >=20.6.0"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"@opentelemetry/core": "2.0.1"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}, "devDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "11.1.0", "nyc": "17.1.0", "ts-loader": "9.5.2", "typescript": "5.0.4"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-propagator-b3", "sideEffects": false, "gitHead": "4ce5bd165195870f292fa95e312cffe05eb9e09d"}