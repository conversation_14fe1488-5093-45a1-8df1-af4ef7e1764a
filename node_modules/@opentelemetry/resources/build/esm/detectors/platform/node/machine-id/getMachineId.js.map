{"version": 3, "file": "getMachineId.js", "sourceRoot": "", "sources": ["../../../../../../src/detectors/platform/node/machine-id/getMachineId.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AAEnC,IAAI,gBAAiE,CAAC;AAEtE,MAAM,CAAC,KAAK,UAAU,YAAY;IAChC,IAAI,CAAC,gBAAgB,EAAE;QACrB,QAAQ,OAAO,CAAC,QAAQ,EAAE;YACxB,KAAK,QAAQ;gBACX,gBAAgB,GAAG,CAAC,MAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;qBAC1D,YAAY,CAAC;gBAChB,MAAM;YACR,KAAK,OAAO;gBACV,gBAAgB,GAAG,CAAC,MAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;qBACzD,YAAY,CAAC;gBAChB,MAAM;YACR,KAAK,SAAS;gBACZ,gBAAgB,GAAG,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC,YAAY,CAAC;gBACxE,MAAM;YACR,KAAK,OAAO;gBACV,gBAAgB,GAAG,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC,YAAY,CAAC;gBACxE,MAAM;YACR;gBACE,gBAAgB,GAAG,CAAC,MAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;qBAC/D,YAAY,CAAC;gBAChB,MAAM;SACT;KACF;IAED,OAAO,gBAAgB,EAAE,CAAC;AAC5B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as process from 'process';\n\nlet getMachineIdImpl: undefined | (() => Promise<string | undefined>);\n\nexport async function getMachineId(): Promise<string | undefined> {\n  if (!getMachineIdImpl) {\n    switch (process.platform) {\n      case 'darwin':\n        getMachineIdImpl = (await import('./getMachineId-darwin.js'))\n          .getMachineId;\n        break;\n      case 'linux':\n        getMachineIdImpl = (await import('./getMachineId-linux.js'))\n          .getMachineId;\n        break;\n      case 'freebsd':\n        getMachineIdImpl = (await import('./getMachineId-bsd.js')).getMachineId;\n        break;\n      case 'win32':\n        getMachineIdImpl = (await import('./getMachineId-win.js')).getMachineId;\n        break;\n      default:\n        getMachineIdImpl = (await import('./getMachineId-unsupported.js'))\n          .getMachineId;\n        break;\n    }\n  }\n\n  return getMachineIdImpl();\n}\n"]}