{"version": 3, "file": "semconv.js", "sourceRoot": "", "sources": ["../../src/semconv.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH;;;;GAIG;AAEH;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;GAIG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;GAIG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,gCAAgC,GAC3C,6BAAsC,CAAC;AAEzC;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,mBAA4B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This file contains a copy of unstable semantic convention definitions\n * used by this package.\n * @see https://github.com/open-telemetry/opentelemetry-js/tree/main/semantic-conventions#unstable-semconv\n */\n\n/**\n * The cloud account ID the resource is assigned to.\n *\n * @example ************\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_ACCOUNT_ID = 'cloud.account.id' as const;\n\n/**\n * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n *\n * @example us-east-1c\n *\n * @note Availability zones are called \"zones\" on Alibaba Cloud and Google Cloud.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone' as const;\n\n/**\n * Name of the cloud provider.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_PROVIDER = 'cloud.provider' as const;\n\n/**\n * The geographical region the resource is running.\n *\n * @example us-central1\n * @example us-east-1\n *\n * @note Refer to your provider's docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/global-infrastructure/geographies/), [Google Cloud regions](https://cloud.google.com/about/locations), or [Tencent Cloud regions](https://www.tencentcloud.com/document/product/213/6091).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_REGION = 'cloud.region' as const;\n\n/**\n * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/containers/run/#container-identification). The UUID might be abbreviated.\n *\n * @example a3bf90e006b2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_ID = 'container.id' as const;\n\n/**\n * Name of the image the container was built on.\n *\n * @example gcr.io/opentelemetry/operator\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_IMAGE_NAME = 'container.image.name' as const;\n\n/**\n * Container image tags. An example can be found in [Docker Image Inspect](https://docs.docker.com/engine/api/v1.43/#tag/Image/operation/ImageInspect). Should be only the `<tag>` section of the full name for example from `registry.example.com/my-org/my-image:<tag>`.\n *\n * @example [\"v1.27.1\", \"3.5.7-0\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_IMAGE_TAGS = 'container.image.tags' as const;\n\n/**\n * Container name used by container runtime.\n *\n * @example opentelemetry-autoconf\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_NAME = 'container.name' as const;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_ARCH = 'host.arch' as const;\n\n/**\n * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider. For non-containerized systems, this should be the `machine-id`. See the table below for the sources to use to determine the `machine-id` based on operating system.\n *\n * @example fdbf79e8af94cb7f9e8df36789187052\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_ID = 'host.id' as const;\n\n/**\n * VM image ID or host OS image ID. For Cloud, this value is from the provider.\n *\n * @example ami-07b06b442921831e5\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IMAGE_ID = 'host.image.id' as const;\n\n/**\n * Name of the VM image or OS install the host was instantiated from.\n *\n * @example infra-ami-eks-worker-node-7d4ec78312\n * @example CentOS-8-x86_64-1905\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IMAGE_NAME = 'host.image.name' as const;\n\n/**\n * The version string of the VM image or host OS as defined in [Version Attributes](/docs/resource/README.md#version-attributes).\n *\n * @example 0.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IMAGE_VERSION = 'host.image.version' as const;\n\n/**\n * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n *\n * @example opentelemetry-test\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_NAME = 'host.name' as const;\n\n/**\n * Type of host. For Cloud, this must be the machine type.\n *\n * @example n1-standard-1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_TYPE = 'host.type' as const;\n\n/**\n * The name of the cluster.\n *\n * @example opentelemetry-cluster\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CLUSTER_NAME = 'k8s.cluster.name' as const;\n\n/**\n * The name of the Deployment.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name' as const;\n\n/**\n * The name of the namespace that the pod is running in.\n *\n * @example default\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NAMESPACE_NAME = 'k8s.namespace.name' as const;\n\n/**\n * The name of the Pod.\n *\n * @example opentelemetry-pod-autoconf\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_POD_NAME = 'k8s.pod.name' as const;\n\n/**\n * The operating system type.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_TYPE = 'os.type' as const;\n\n/**\n * The version string of the operating system as defined in [Version Attributes](/docs/resource/README.md#version-attributes).\n *\n * @example 14.2.1\n * @example 18.04.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_VERSION = 'os.version' as const;\n\n/**\n * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n *\n * @example cmd/otelcol\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_COMMAND = 'process.command' as const;\n\n/**\n * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`.\n *\n * @example [\"cmd/otecol\", \"--config=config.yaml\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_COMMAND_ARGS = 'process.command_args' as const;\n\n/**\n * The name of the process executable. On Linux based systems, this **SHOULD** be set to the base name of the target of `/proc/[pid]/exe`. On Windows, this **SHOULD** be set to the base name of `GetProcessImageFileNameW`.\n *\n * @example otelcol\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_NAME = 'process.executable.name' as const;\n\n/**\n * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n *\n * @example /usr/bin/cmd/otelcol\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_PATH = 'process.executable.path' as const;\n\n/**\n * The username of the user that owns the process.\n *\n * @example root\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_OWNER = 'process.owner' as const;\n\n/**\n * Process identifier (PID).\n *\n * @example 1234\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_PID = 'process.pid' as const;\n\n/**\n * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n *\n * @example \"Eclipse OpenJ9 Eclipse OpenJ9 VM openj9-0.21.0\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_RUNTIME_DESCRIPTION =\n  'process.runtime.description' as const;\n\n/**\n * The name of the runtime of this process.\n *\n * @example OpenJDK Runtime Environment\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_RUNTIME_NAME = 'process.runtime.name' as const;\n\n/**\n * The version of the runtime of this process, as returned by the runtime without modification.\n *\n * @example \"14.0.2\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_RUNTIME_VERSION = 'process.runtime.version' as const;\n\n/**\n * The string ID of the service instance.\n *\n * @example 627cc493-f310-47de-96bd-71410b7dec09\n *\n * @note **MUST** be unique for each instance of the same `service.namespace,service.name` pair (in other words\n * `service.namespace,service.name,service.instance.id` triplet **MUST** be globally unique). The ID helps to\n * distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled\n * service).\n *\n * Implementations, such as SDKs, are recommended to generate a random Version 1 or Version 4 [RFC\n * 4122](https://www.ietf.org/rfc/rfc4122.txt) UUID, but are free to use an inherent unique ID as the source of\n * this value if stability is desirable. In that case, the ID **SHOULD** be used as source of a UUID Version 5 and\n * **SHOULD** use the following UUID as the namespace: `4d63009a-8d0f-11ee-aad7-4c796ed8e320`.\n *\n * UUIDs are typically recommended, as only an opaque value for the purposes of identifying a service instance is\n * needed. Similar to what can be seen in the man page for the\n * [`/etc/machine-id`](https://www.freedesktop.org/software/systemd/man/latest/machine-id.html) file, the underlying\n * data, such as pod name and namespace should be treated as confidential, being the user's choice to expose it\n * or not via another resource attribute.\n *\n * For applications running behind an application server (like unicorn), we do not recommend using one identifier\n * for all processes participating in the application. Instead, it's recommended each division (e.g. a worker\n * thread in unicorn) to have its own instance.id.\n *\n * It's not recommended for a Collector to set `service.instance.id` if it can't unambiguously determine the\n * service instance that is generating that telemetry. For instance, creating an UUID based on `pod.name` will\n * likely be wrong, as the Collector might not know from which container within that pod the telemetry originated.\n * However, Collectors can set the `service.instance.id` if they can unambiguously determine the service instance\n * for that telemetry. This is typically the case for scraping receivers, as they know the target address and\n * port.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SERVICE_INSTANCE_ID = 'service.instance.id' as const;\n\n/**\n * A namespace for `service.name`.\n *\n * @example Shop\n *\n * @note A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SERVICE_NAMESPACE = 'service.namespace' as const;\n\n/**\n * Additional description of the web engine (e.g. detailed version and edition information).\n *\n * @example WildFly Full 21.0.0.Final (WildFly Core 13.0.1.Final) - 2.2.2.Final\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_WEBENGINE_DESCRIPTION = 'webengine.description' as const;\n\n/**\n * The name of the web engine.\n *\n * @example WildFly\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_WEBENGINE_NAME = 'webengine.name' as const;\n\n/**\n * The version of the web engine.\n *\n * @example 21.0.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_WEBENGINE_VERSION = 'webengine.version' as const;\n"]}