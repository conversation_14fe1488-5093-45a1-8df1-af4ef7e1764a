{"version": 3, "file": "detect-resources.js", "sourceRoot": "", "sources": ["../../src/detect-resources.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,aAAa,EAAE,4BAA4B,EAAE,MAAM,gBAAgB,CAAC;AAG7E;;;;GAIG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAC7B,SAAkC,EAAE,EAC1B,EAAE;IACZ,MAAM,SAAS,GAAe,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QAC7D,IAAI;YACF,MAAM,QAAQ,GAAG,4BAA4B,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAC9D,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACzD,OAAO,aAAa,EAAE,CAAC;SACxB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC,MAAM,CACrB,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EACtC,aAAa,EAAE,CAChB,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { Resource } from './Resource';\nimport { emptyResource, resourceFromDetectedResource } from './ResourceImpl';\nimport { ResourceDetectionConfig } from './config';\n\n/**\n * Runs all resource detectors and returns the results merged into a single Resource.\n *\n * @param config Configuration for resource detection\n */\nexport const detectResources = (\n  config: ResourceDetectionConfig = {}\n): Resource => {\n  const resources: Resource[] = (config.detectors || []).map(d => {\n    try {\n      const resource = resourceFromDetectedResource(d.detect(config));\n      diag.debug(`${d.constructor.name} found resource.`, resource);\n      return resource;\n    } catch (e) {\n      diag.debug(`${d.constructor.name} failed: ${e.message}`);\n      return emptyResource();\n    }\n  });\n\n  return resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    emptyResource()\n  );\n};\n"]}