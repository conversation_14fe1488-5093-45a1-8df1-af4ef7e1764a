{"version": 3, "file": "ProcessDetector.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/ProcessDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAc,IAAI,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EACL,oBAAoB,EACpB,yBAAyB,EACzB,4BAA4B,EAC5B,4BAA4B,EAC5B,kBAAkB,EAClB,gBAAgB,EAChB,gCAAgC,EAChC,yBAAyB,EACzB,4BAA4B,GAC7B,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAIzB;;;GAGG;AACH,MAAM,eAAe;IACnB,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAe;YAC7B,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC,GAAG;YAC/B,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC,KAAK;YAC7C,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC,QAAQ;YAChD,CAAC,yBAAyB,CAAC,EAAE;gBAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACf,GAAG,OAAO,CAAC,QAAQ;gBACnB,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACzB;YACD,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;YACrD,CAAC,yBAAyB,CAAC,EAAE,QAAQ;YACrC,CAAC,gCAAgC,CAAC,EAAE,SAAS;SAC9C,CAAC;QAEF,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,UAAU,CAAC,oBAAoB,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACpD;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC/B,UAAU,CAAC,kBAAkB,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;SACpD;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,EAAE,CAAC,CAAC;SACnD;QAED,OAAO,EAAE,UAAU,EAAE,CAAC;IACxB,CAAC;CACF;AAED,MAAM,CAAC,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes, diag } from '@opentelemetry/api';\nimport {\n  ATTR_PROCESS_COMMAND,\n  ATTR_PROCESS_COMMAND_ARGS,\n  ATTR_PROCESS_EXECUTABLE_NAME,\n  ATTR_PROCESS_EXECUTABLE_PATH,\n  ATTR_PROCESS_OWNER,\n  ATTR_PROCESS_PID,\n  ATTR_PROCESS_RUNTIME_DESCRIPTION,\n  ATTR_PROCESS_RUNTIME_NAME,\n  ATTR_PROCESS_RUNTIME_VERSION,\n} from '../../../semconv';\nimport * as os from 'os';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { DetectedResource, ResourceDetector } from '../../../types';\n\n/**\n * ProcessDetector will be used to detect the resources related current process running\n * and being instrumented from the NodeJS Process module.\n */\nclass ProcessDetector implements ResourceDetector {\n  detect(_config?: ResourceDetectionConfig): DetectedResource {\n    const attributes: Attributes = {\n      [ATTR_PROCESS_PID]: process.pid,\n      [ATTR_PROCESS_EXECUTABLE_NAME]: process.title,\n      [ATTR_PROCESS_EXECUTABLE_PATH]: process.execPath,\n      [ATTR_PROCESS_COMMAND_ARGS]: [\n        process.argv[0],\n        ...process.execArgv,\n        ...process.argv.slice(1),\n      ],\n      [ATTR_PROCESS_RUNTIME_VERSION]: process.versions.node,\n      [ATTR_PROCESS_RUNTIME_NAME]: 'nodejs',\n      [ATTR_PROCESS_RUNTIME_DESCRIPTION]: 'Node.js',\n    };\n\n    if (process.argv.length > 1) {\n      attributes[ATTR_PROCESS_COMMAND] = process.argv[1];\n    }\n\n    try {\n      const userInfo = os.userInfo();\n      attributes[ATTR_PROCESS_OWNER] = userInfo.username;\n    } catch (e) {\n      diag.debug(`error obtaining process owner: ${e}`);\n    }\n\n    return { attributes };\n  }\n}\n\nexport const processDetector = new ProcessDetector();\n"]}