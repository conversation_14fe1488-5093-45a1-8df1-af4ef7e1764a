{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../../src/sdk.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAC5B,sDAA+C;AAC/C,oEAGwC;AACxC,wDAUkC;AAClC,sDAOiC;AACjC,oFAAgG;AAChG,oFAAgG;AAChG,sFAAkG;AAClG,0FAAyG;AACzG,4FAA2G;AAC3G,0FAAyG;AACzG,4EAAoG;AACpG,4DAMoC;AACpC,kEAGuC;AACvC,kEAGuC;AACvC,8EAAwE;AAExE,8CAK6B;AAC7B,mCAIiB;AAsBjB;;GAEG;AACH,SAAS,gBAAgB,CAAC,OAAe,EAAE,YAAoB;IAC7D,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,YAAY,CAAC;AAC9D,CAAC;AAED;;;GAGG;AACH,SAAS,8BAA8B;IACrC,MAAM,aAAa,GAAoB,EAAE,CAAC;IAC1C,MAAM,gBAAgB,GAAG,IAAA,2BAAoB,EAAC,uBAAuB,CAAC,CAAC;IACvE,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO,aAAa,CAAC;KACtB;IAED,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;QACjC,UAAI,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;KAC5E;IAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACrC,UAAI,CAAC,IAAI,CACP,iFAAiF,CAClF,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IACD,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QAClC,IAAI,QAAQ,KAAK,MAAM,EAAE;YACvB,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,EAAE;gBACvD,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,IAAI,EAAE,CAAC;YAElD,MAAM,oBAAoB,GAAG,gBAAgB,CAC3C,6BAA6B,EAC7B,KAAK,CACN,CAAC;YACF,MAAM,mBAAmB,GAAG,gBAAgB,CAC1C,4BAA4B,EAC5B,KAAK,CACN,CAAC;YAEF,QAAQ,QAAQ,EAAE;gBAChB,KAAK,MAAM;oBACT,aAAa,CAAC,IAAI,CAChB,IAAI,2CAA6B,CAAC;wBAChC,QAAQ,EAAE,IAAI,+CAAsB,EAAE;wBACtC,oBAAoB,EAAE,oBAAoB;wBAC1C,mBAAmB,EAAE,mBAAmB;qBACzC,CAAC,CACH,CAAC;oBACF,MAAM;gBACR,KAAK,WAAW;oBACd,aAAa,CAAC,IAAI,CAChB,IAAI,2CAA6B,CAAC;wBAChC,QAAQ,EAAE,IAAI,+CAAsB,EAAE;wBACtC,oBAAoB,EAAE,oBAAoB;wBAC1C,mBAAmB,EAAE,mBAAmB;qBACzC,CAAC,CACH,CAAC;oBACF,MAAM;gBACR,KAAK,eAAe;oBAClB,aAAa,CAAC,IAAI,CAChB,IAAI,2CAA6B,CAAC;wBAChC,QAAQ,EAAE,IAAI,gDAAuB,EAAE;wBACvC,oBAAoB,EAAE,oBAAoB;wBAC1C,mBAAmB,EAAE,mBAAmB;qBACzC,CAAC,CACH,CAAC;oBACF,MAAM;gBACR;oBACE,UAAI,CAAC,IAAI,CACP,uCAAuC,QAAQ,yBAAyB,CACzE,CAAC;oBACF,aAAa,CAAC,IAAI,CAChB,IAAI,2CAA6B,CAAC;wBAChC,QAAQ,EAAE,IAAI,gDAAuB,EAAE;wBACvC,oBAAoB,EAAE,oBAAoB;wBAC1C,mBAAmB,EAAE,mBAAmB;qBACzC,CAAC,CACH,CAAC;aACL;SACF;aAAM,IAAI,QAAQ,KAAK,SAAS,EAAE;YACjC,aAAa,CAAC,IAAI,CAChB,IAAI,2CAA6B,CAAC;gBAChC,QAAQ,EAAE,IAAI,mCAAqB,EAAE;aACtC,CAAC,CACH,CAAC;SACH;aAAM,IAAI,QAAQ,KAAK,YAAY,EAAE;YACpC,aAAa,CAAC,IAAI,CAAC,IAAI,wCAAwB,EAAE,CAAC,CAAC;SACpD;aAAM;YACL,UAAI,CAAC,IAAI,CACP,6CAA6C,QAAQ,2DAA2D,CACjH,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,aAAa,CAAC;AACvB,CAAC;AACD,MAAa,OAAO;IACV,qBAAqB,CAK3B;IACM,qBAAqB,CAAwB;IAC7C,oBAAoB,CAAuB;IAC3C,iBAAiB,CAAoB;IAErC,SAAS,CAAW;IACpB,kBAAkB,CAA0B;IAE5C,oBAAoB,CAAU;IAE9B,eAAe,CAAsB;IACrC,eAAe,CAAkB;IACjC,cAAc,CAAiB;IAC/B,YAAY,CAAU;IACtB,cAAc,CAAiC;IAE/C,SAAS,CAAW;IAE5B;;OAEG;IACH,YAAmB,gBAA+C,EAAE;QAClE,IAAI,IAAA,wBAAiB,EAAC,mBAAmB,CAAC,EAAE;YAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,+CAA+C;YAC/C,kCAAkC;SACnC;QAED,MAAM,QAAQ,GAAG,IAAA,uBAAgB,EAAC,gBAAgB,CAAC,CAAC;QACpD,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,UAAI,CAAC,SAAS,CAAC,IAAI,uBAAiB,EAAE,EAAE;gBACtC,QAAQ,EAAE,IAAA,6BAAsB,EAAC,QAAQ,CAAC;aAC3C,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,QAAQ,IAAI,IAAA,2BAAe,GAAE,CAAC;QAC7D,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,mBAAmB,IAAI,IAAI,CAAC;QACtE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAC9B;aAAM,IAAI,aAAa,CAAC,iBAAiB,IAAI,IAAI,EAAE;YAClD,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,iBAAiB,CAAC;SAC3D;aAAM,IAAI,IAAA,uBAAgB,EAAC,8BAA8B,CAAC,EAAE;YAC3D,IAAI,CAAC,kBAAkB,GAAG,IAAA,mCAA2B,GAAE,CAAC;SACzD;aAAM;YACL,IAAI,CAAC,kBAAkB,GAAG,CAAC,uBAAW,EAAE,2BAAe,EAAE,wBAAY,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC;QAE9C,2EAA2E;QAC3E,IACE,aAAa,CAAC,aAAa;YAC3B,aAAa,CAAC,aAAa;YAC3B,aAAa,CAAC,cAAc,EAC5B;YACA,MAAM,oBAAoB,GAAqB,EAAE,CAAC;YAElD,IAAI,aAAa,CAAC,OAAO,EAAE;gBACzB,oBAAoB,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;aACtD;YACD,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC5B,oBAAoB,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;aAC5D;YACD,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC7B,oBAAoB,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;aAC9D;YAED,IAAI,aAAa,CAAC,aAAa,EAAE;gBAC/B,UAAI,CAAC,IAAI,CACP,gFAAgF,CACjF,CAAC;aACH;YAED,MAAM,aAAa,GACjB,aAAa,CAAC,aAAa;gBAC3B,oEAAoE;gBACpE,IAAI,mCAAkB,CAAC,aAAa,CAAC,aAAc,CAAC,CAAC;YAEvD,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,IAAI,CAAC,aAAa,CAAC,CAAC;YAEvE,IAAI,CAAC,qBAAqB,GAAG;gBAC3B,YAAY,EAAE,oBAAoB;gBAClC,cAAc;gBACd,cAAc,EAAE,aAAa,CAAC,cAAc;gBAC5C,iBAAiB,EAAE,aAAa,CAAC,iBAAiB;aACnD,CAAC;SACH;QAED,IAAI,aAAa,CAAC,mBAAmB,EAAE;YACrC,IAAI,CAAC,qBAAqB,GAAG;gBAC3B,mBAAmB,EAAE,aAAa,CAAC,mBAAmB;aACvD,CAAC;SACH;aAAM,IAAI,aAAa,CAAC,kBAAkB,EAAE;YAC3C,IAAI,CAAC,qBAAqB,GAAG;gBAC3B,mBAAmB,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC;aACxD,CAAC;YACF,UAAI,CAAC,IAAI,CACP,0FAA0F,CAC3F,CAAC;SACH;aAAM;YACL,IAAI,CAAC,8BAA8B,EAAE,CAAC;SACvC;QAED,IAAI,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,KAAK,EAAE;YACrD,MAAM,mBAAmB,GAAwB,EAAE,CAAC;YACpD,IAAI,aAAa,CAAC,YAAY,EAAE;gBAC9B,mBAAmB,CAAC,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC;aACzD;YAED,IAAI,aAAa,CAAC,KAAK,EAAE;gBACvB,mBAAmB,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;aACjD;YAED,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;SACjD;QAED,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QAED,IAAA,0CAAwB,EAAC;YACvB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,cAAc,GAA4B;gBAC9C,SAAS,EAAE,IAAI,CAAC,kBAAkB;aACnC,CAAC;YAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAA,2BAAe,EAAC,cAAc,CAAC,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,YAAY,KAAK,SAAS;gBAC7B,CAAC,CAAC,IAAI,CAAC,SAAS;gBAChB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAClB,IAAA,kCAAsB,EAAC;oBACrB,CAAC,wCAAiB,CAAC,EAAE,IAAI,CAAC,YAAY;iBACvC,CAAC,CACH,CAAC;QAER,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB;YAC/C,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc;YAC3C,CAAC,CAAC,IAAA,gCAAwB,GAAE,CAAC;QAE/B,IAAI,CAAC,eAAe,GAAG,IAAI,mCAAkB,CAAC;YAC5C,GAAG,IAAI,CAAC,cAAc;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,cAAc;SACf,CAAC,CAAC;QAEH,6CAA6C;QAC7C,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAC5B,cAAc,EACZ,IAAI,CAAC,qBAAqB,EAAE,cAAc;oBAC1C,+GAA+G;oBAC/G,IAAI,CAAC,cAAc,EAAE,cAAc;gBACrC,UAAU,EACR,IAAI,CAAC,qBAAqB,EAAE,iBAAiB;oBAC7C,IAAA,4BAAoB,GAAE;aACzB,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,MAAM,cAAc,GAAG,IAAI,yBAAc,CAAC;gBACxC,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,UAAU,EAAE,IAAI,CAAC,qBAAqB,CAAC,mBAAmB;aAC3D,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;YAEtC,eAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;SAC9C;QAED,MAAM,oBAAoB,GACxB,8BAA8B,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YAChE,MAAM,OAAO,GAAoB,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,oBAAoB,EAAE,MAAM,EAAE;gBACrC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aAChD;YAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACrE;YAED,MAAM,aAAa,GAAG,IAAI,2BAAa,CAAC;gBACtC,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;gBAC7C,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YACpC,aAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAE9C,mGAAmG;YACnG,4GAA4G;YAC5G,4GAA4G;YAC5G,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACpD,eAAe,CAAC,gBAAgB,CAAC,aAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC9D;SACF;IACH,CAAC;IAEM,QAAQ;QACb,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC/C;QAED,OAAO,CACL,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;YACnB,oDAAoD;aACnD,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAClB,CAAC;IACJ,CAAC;IAEO,8BAA8B;QACpC,MAAM,gBAAgB,GAAG,IAAA,2BAAoB,EAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QAE1E,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,UAAI,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YACxE,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/B;QAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACrC,UAAI,CAAC,IAAI,CACP,8EAA8E,CAC/E,CAAC;YACF,OAAO;SACR;QAED,MAAM,SAAS,GAAwB,EAAE,CAAC;QAE1C,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAClC,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACvB,MAAM,QAAQ,GAAG,CACf,IAAA,uBAAgB,EAAC,kCAAkC,CAAC;oBACpD,IAAA,uBAAgB,EAAC,6BAA6B,CAAC,CAChD,EAAE,IAAI,EAAE,CAAC;gBAEV,QAAQ,QAAQ,EAAE;oBAChB,KAAK,MAAM;wBACT,SAAS,CAAC,IAAI,CAAC,IAAI,yCAAmB,EAAE,CAAC,CAAC;wBAC1C,MAAM;oBACR,KAAK,WAAW;wBACd,SAAS,CAAC,IAAI,CAAC,IAAI,yCAAmB,EAAE,CAAC,CAAC;wBAC1C,MAAM;oBACR,KAAK,eAAe;wBAClB,SAAS,CAAC,IAAI,CAAC,IAAI,0CAAoB,EAAE,CAAC,CAAC;wBAC3C,MAAM;oBACR,KAAK,SAAS,CAAC;oBACf,KAAK,EAAE;wBACL,SAAS,CAAC,IAAI,CAAC,IAAI,0CAAoB,EAAE,CAAC,CAAC;wBAC3C,MAAM;oBACR;wBACE,UAAI,CAAC,IAAI,CACP,oCAAoC,QAAQ,yBAAyB,CACtE,CAAC;wBACF,SAAS,CAAC,IAAI,CAAC,IAAI,0CAAoB,EAAE,CAAC,CAAC;iBAC9C;aACF;iBAAM,IAAI,QAAQ,KAAK,SAAS,EAAE;gBACjC,SAAS,CAAC,IAAI,CAAC,IAAI,mCAAwB,EAAE,CAAC,CAAC;aAChD;iBAAM;gBACL,UAAI,CAAC,IAAI,CACP,0CAA0C,QAAQ,+CAA+C,CAClG,CAAC;aACH;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,qBAAqB,GAAG;gBAC3B,mBAAmB,EAAE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;oBAC5C,IAAI,QAAQ,YAAY,mCAAwB,EAAE;wBAChD,OAAO,IAAI,mCAAwB,CAAC,QAAQ,CAAC,CAAC;qBAC/C;yBAAM;wBACL,OAAO,IAAI,kCAAuB,CAAC,QAAQ,CAAC,CAAC;qBAC9C;gBACH,CAAC,CAAC;aACH,CAAC;SACH;IACH,CAAC;CACF;AAhTD,0BAgTC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ContextManager,\n  TextMapPropagator,\n  metrics,\n  diag,\n  DiagConsoleLogger,\n} from '@opentelemetry/api';\nimport { logs } from '@opentelemetry/api-logs';\nimport {\n  Instrumentation,\n  registerInstrumentations,\n} from '@opentelemetry/instrumentation';\nimport {\n  defaultResource,\n  detectResources,\n  envDetector,\n  hostDetector,\n  Resource,\n  processDetector,\n  ResourceDetectionConfig,\n  ResourceDetector,\n  resourceFromAttributes,\n} from '@opentelemetry/resources';\nimport {\n  LogRecordProcessor,\n  LoggerProvider,\n  BatchLogRecordProcessor,\n  ConsoleLogRecordExporter,\n  LogRecordExporter,\n  SimpleLogRecordProcessor,\n} from '@opentelemetry/sdk-logs';\nimport { OTLPLogExporter as OTLPHttpLogExporter } from '@opentelemetry/exporter-logs-otlp-http';\nimport { OTLPLogExporter as OTLPGrpcLogExporter } from '@opentelemetry/exporter-logs-otlp-grpc';\nimport { OTLPLogExporter as OTLPProtoLogExporter } from '@opentelemetry/exporter-logs-otlp-proto';\nimport { OTLPMetricExporter as OTLPGrpcMetricExporter } from '@opentelemetry/exporter-metrics-otlp-grpc';\nimport { OTLPMetricExporter as OTLPProtoMetricExporter } from '@opentelemetry/exporter-metrics-otlp-proto';\nimport { OTLPMetricExporter as OTLPHttpMetricExporter } from '@opentelemetry/exporter-metrics-otlp-http';\nimport { PrometheusExporter as PrometheusMetricExporter } from '@opentelemetry/exporter-prometheus';\nimport {\n  MeterProvider,\n  IMetricReader,\n  ViewOptions,\n  ConsoleMetricExporter,\n  PeriodicExportingMetricReader,\n} from '@opentelemetry/sdk-metrics';\nimport {\n  BatchSpanProcessor,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport {\n  NodeTracerConfig,\n  NodeTracerProvider,\n} from '@opentelemetry/sdk-trace-node';\nimport { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';\nimport { NodeSDKConfiguration } from './types';\nimport {\n  getBooleanFromEnv,\n  getStringFromEnv,\n  getStringListFromEnv,\n  diagLogLevelFromString,\n} from '@opentelemetry/core';\nimport {\n  getResourceDetectorsFromEnv,\n  getSpanProcessorsFromEnv,\n  getPropagatorFromEnv,\n} from './utils';\n\n/** This class represents everything needed to register a fully configured OpenTelemetry Node.js SDK */\n\nexport type MeterProviderConfig = {\n  /**\n   * Reference to the MetricReader instance by the NodeSDK\n   */\n  reader?: IMetricReader;\n  /**\n   * List of {@link ViewOptions}s that should be passed to the MeterProvider\n   */\n  views?: ViewOptions[];\n};\n\nexport type LoggerProviderConfig = {\n  /**\n   * Reference to the LoggerRecordProcessor instance by the NodeSDK\n   */\n  logRecordProcessors: LogRecordProcessor[];\n};\n\n/**\n * @Returns param value, if set else returns the default value\n */\nfunction getValueInMillis(envName: string, defaultValue: number): number {\n  return parseInt(process.env[envName] || '') || defaultValue;\n}\n\n/**\n *\n * @returns MetricReader[] if appropriate environment variables are configured\n */\nfunction configureMetricProviderFromEnv(): IMetricReader[] {\n  const metricReaders: IMetricReader[] = [];\n  const enabledExporters = getStringListFromEnv('OTEL_METRICS_EXPORTER');\n  if (!enabledExporters) {\n    return metricReaders;\n  }\n\n  if (enabledExporters.length === 0) {\n    diag.debug('OTEL_METRICS_EXPORTER is empty. Using default otlp exporter.');\n  }\n\n  if (enabledExporters.includes('none')) {\n    diag.info(\n      `OTEL_METRICS_EXPORTER contains \"none\". Metric provider will not be initialized.`\n    );\n    return metricReaders;\n  }\n  enabledExporters.forEach(exporter => {\n    if (exporter === 'otlp') {\n      const protocol =\n        process.env.OTEL_EXPORTER_OTLP_METRICS_PROTOCOL?.trim() ||\n        process.env.OTEL_EXPORTER_OTLP_PROTOCOL?.trim();\n\n      const exportIntervalMillis = getValueInMillis(\n        'OTEL_METRIC_EXPORT_INTERVAL',\n        60000\n      );\n      const exportTimeoutMillis = getValueInMillis(\n        'OTEL_METRIC_EXPORT_TIMEOUT',\n        30000\n      );\n\n      switch (protocol) {\n        case 'grpc':\n          metricReaders.push(\n            new PeriodicExportingMetricReader({\n              exporter: new OTLPGrpcMetricExporter(),\n              exportIntervalMillis: exportIntervalMillis,\n              exportTimeoutMillis: exportTimeoutMillis,\n            })\n          );\n          break;\n        case 'http/json':\n          metricReaders.push(\n            new PeriodicExportingMetricReader({\n              exporter: new OTLPHttpMetricExporter(),\n              exportIntervalMillis: exportIntervalMillis,\n              exportTimeoutMillis: exportTimeoutMillis,\n            })\n          );\n          break;\n        case 'http/protobuf':\n          metricReaders.push(\n            new PeriodicExportingMetricReader({\n              exporter: new OTLPProtoMetricExporter(),\n              exportIntervalMillis: exportIntervalMillis,\n              exportTimeoutMillis: exportTimeoutMillis,\n            })\n          );\n          break;\n        default:\n          diag.warn(\n            `Unsupported OTLP metrics protocol: \"${protocol}\". Using http/protobuf.`\n          );\n          metricReaders.push(\n            new PeriodicExportingMetricReader({\n              exporter: new OTLPProtoMetricExporter(),\n              exportIntervalMillis: exportIntervalMillis,\n              exportTimeoutMillis: exportTimeoutMillis,\n            })\n          );\n      }\n    } else if (exporter === 'console') {\n      metricReaders.push(\n        new PeriodicExportingMetricReader({\n          exporter: new ConsoleMetricExporter(),\n        })\n      );\n    } else if (exporter === 'prometheus') {\n      metricReaders.push(new PrometheusMetricExporter());\n    } else {\n      diag.warn(\n        `Unsupported OTEL_METRICS_EXPORTER value: \"${exporter}\". Supported values are: otlp, console, prometheus, none.`\n      );\n    }\n  });\n\n  return metricReaders;\n}\nexport class NodeSDK {\n  private _tracerProviderConfig?: {\n    tracerConfig: NodeTracerConfig;\n    spanProcessors: SpanProcessor[];\n    contextManager?: ContextManager;\n    textMapPropagator?: TextMapPropagator;\n  };\n  private _loggerProviderConfig?: LoggerProviderConfig;\n  private _meterProviderConfig?: MeterProviderConfig;\n  private _instrumentations: Instrumentation[];\n\n  private _resource: Resource;\n  private _resourceDetectors: Array<ResourceDetector>;\n\n  private _autoDetectResources: boolean;\n\n  private _tracerProvider?: NodeTracerProvider;\n  private _loggerProvider?: LoggerProvider;\n  private _meterProvider?: MeterProvider;\n  private _serviceName?: string;\n  private _configuration?: Partial<NodeSDKConfiguration>;\n\n  private _disabled?: boolean;\n\n  /**\n   * Create a new NodeJS SDK instance\n   */\n  public constructor(configuration: Partial<NodeSDKConfiguration> = {}) {\n    if (getBooleanFromEnv('OTEL_SDK_DISABLED')) {\n      this._disabled = true;\n      // Functions with possible side-effects are set\n      // to no-op via the _disabled flag\n    }\n\n    const logLevel = getStringFromEnv('OTEL_LOG_LEVEL');\n    if (logLevel != null) {\n      diag.setLogger(new DiagConsoleLogger(), {\n        logLevel: diagLogLevelFromString(logLevel),\n      });\n    }\n\n    this._configuration = configuration;\n\n    this._resource = configuration.resource ?? defaultResource();\n    this._autoDetectResources = configuration.autoDetectResources ?? true;\n    if (!this._autoDetectResources) {\n      this._resourceDetectors = [];\n    } else if (configuration.resourceDetectors != null) {\n      this._resourceDetectors = configuration.resourceDetectors;\n    } else if (getStringFromEnv('OTEL_NODE_RESOURCE_DETECTORS')) {\n      this._resourceDetectors = getResourceDetectorsFromEnv();\n    } else {\n      this._resourceDetectors = [envDetector, processDetector, hostDetector];\n    }\n\n    this._serviceName = configuration.serviceName;\n\n    // If a tracer provider can be created from manual configuration, create it\n    if (\n      configuration.traceExporter ||\n      configuration.spanProcessor ||\n      configuration.spanProcessors\n    ) {\n      const tracerProviderConfig: NodeTracerConfig = {};\n\n      if (configuration.sampler) {\n        tracerProviderConfig.sampler = configuration.sampler;\n      }\n      if (configuration.spanLimits) {\n        tracerProviderConfig.spanLimits = configuration.spanLimits;\n      }\n      if (configuration.idGenerator) {\n        tracerProviderConfig.idGenerator = configuration.idGenerator;\n      }\n\n      if (configuration.spanProcessor) {\n        diag.warn(\n          \"The 'spanProcessor' option is deprecated. Please use 'spanProcessors' instead.\"\n        );\n      }\n\n      const spanProcessor =\n        configuration.spanProcessor ??\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        new BatchSpanProcessor(configuration.traceExporter!);\n\n      const spanProcessors = configuration.spanProcessors ?? [spanProcessor];\n\n      this._tracerProviderConfig = {\n        tracerConfig: tracerProviderConfig,\n        spanProcessors,\n        contextManager: configuration.contextManager,\n        textMapPropagator: configuration.textMapPropagator,\n      };\n    }\n\n    if (configuration.logRecordProcessors) {\n      this._loggerProviderConfig = {\n        logRecordProcessors: configuration.logRecordProcessors,\n      };\n    } else if (configuration.logRecordProcessor) {\n      this._loggerProviderConfig = {\n        logRecordProcessors: [configuration.logRecordProcessor],\n      };\n      diag.warn(\n        \"The 'logRecordProcessor' option is deprecated. Please use 'logRecordProcessors' instead.\"\n      );\n    } else {\n      this.configureLoggerProviderFromEnv();\n    }\n\n    if (configuration.metricReader || configuration.views) {\n      const meterProviderConfig: MeterProviderConfig = {};\n      if (configuration.metricReader) {\n        meterProviderConfig.reader = configuration.metricReader;\n      }\n\n      if (configuration.views) {\n        meterProviderConfig.views = configuration.views;\n      }\n\n      this._meterProviderConfig = meterProviderConfig;\n    }\n\n    this._instrumentations = configuration.instrumentations?.flat() ?? [];\n  }\n\n  /**\n   * Call this method to construct SDK components and register them with the OpenTelemetry API.\n   */\n  public start(): void {\n    if (this._disabled) {\n      return;\n    }\n\n    registerInstrumentations({\n      instrumentations: this._instrumentations,\n    });\n\n    if (this._autoDetectResources) {\n      const internalConfig: ResourceDetectionConfig = {\n        detectors: this._resourceDetectors,\n      };\n\n      this._resource = this._resource.merge(detectResources(internalConfig));\n    }\n\n    this._resource =\n      this._serviceName === undefined\n        ? this._resource\n        : this._resource.merge(\n            resourceFromAttributes({\n              [ATTR_SERVICE_NAME]: this._serviceName,\n            })\n          );\n\n    const spanProcessors = this._tracerProviderConfig\n      ? this._tracerProviderConfig.spanProcessors\n      : getSpanProcessorsFromEnv();\n\n    this._tracerProvider = new NodeTracerProvider({\n      ...this._configuration,\n      resource: this._resource,\n      spanProcessors,\n    });\n\n    // Only register if there is a span processor\n    if (spanProcessors.length > 0) {\n      this._tracerProvider.register({\n        contextManager:\n          this._tracerProviderConfig?.contextManager ??\n          // _tracerProviderConfig may be undefined if trace-specific settings are not provided - fall back to raw config\n          this._configuration?.contextManager,\n        propagator:\n          this._tracerProviderConfig?.textMapPropagator ??\n          getPropagatorFromEnv(),\n      });\n    }\n\n    if (this._loggerProviderConfig) {\n      const loggerProvider = new LoggerProvider({\n        resource: this._resource,\n        processors: this._loggerProviderConfig.logRecordProcessors,\n      });\n\n      this._loggerProvider = loggerProvider;\n\n      logs.setGlobalLoggerProvider(loggerProvider);\n    }\n\n    const metricReadersFromEnv: IMetricReader[] =\n      configureMetricProviderFromEnv();\n    if (this._meterProviderConfig || metricReadersFromEnv.length > 0) {\n      const readers: IMetricReader[] = [];\n      if (this._meterProviderConfig?.reader) {\n        readers.push(this._meterProviderConfig.reader);\n      }\n\n      if (readers.length === 0) {\n        metricReadersFromEnv.forEach((r: IMetricReader) => readers.push(r));\n      }\n\n      const meterProvider = new MeterProvider({\n        resource: this._resource,\n        views: this._meterProviderConfig?.views ?? [],\n        readers: readers,\n      });\n\n      this._meterProvider = meterProvider;\n      metrics.setGlobalMeterProvider(meterProvider);\n\n      // TODO: This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/3609\n      // If the MeterProvider is not yet registered when instrumentations are registered, all metrics are dropped.\n      // This code is obsolete once https://github.com/open-telemetry/opentelemetry-js/issues/3622 is implemented.\n      for (const instrumentation of this._instrumentations) {\n        instrumentation.setMeterProvider(metrics.getMeterProvider());\n      }\n    }\n  }\n\n  public shutdown(): Promise<void> {\n    const promises: Promise<unknown>[] = [];\n    if (this._tracerProvider) {\n      promises.push(this._tracerProvider.shutdown());\n    }\n    if (this._loggerProvider) {\n      promises.push(this._loggerProvider.shutdown());\n    }\n    if (this._meterProvider) {\n      promises.push(this._meterProvider.shutdown());\n    }\n\n    return (\n      Promise.all(promises)\n        // return void instead of the array from Promise.all\n        .then(() => {})\n    );\n  }\n\n  private configureLoggerProviderFromEnv(): void {\n    const enabledExporters = getStringListFromEnv('OTEL_LOGS_EXPORTER') ?? [];\n\n    if (enabledExporters.length === 0) {\n      diag.debug('OTEL_LOGS_EXPORTER is empty. Using default otlp exporter.');\n      enabledExporters.push('otlp');\n    }\n\n    if (enabledExporters.includes('none')) {\n      diag.info(\n        `OTEL_LOGS_EXPORTER contains \"none\". Logger provider will not be initialized.`\n      );\n      return;\n    }\n\n    const exporters: LogRecordExporter[] = [];\n\n    enabledExporters.forEach(exporter => {\n      if (exporter === 'otlp') {\n        const protocol = (\n          getStringFromEnv('OTEL_EXPORTER_OTLP_LOGS_PROTOCOL') ??\n          getStringFromEnv('OTEL_EXPORTER_OTLP_PROTOCOL')\n        )?.trim();\n\n        switch (protocol) {\n          case 'grpc':\n            exporters.push(new OTLPGrpcLogExporter());\n            break;\n          case 'http/json':\n            exporters.push(new OTLPHttpLogExporter());\n            break;\n          case 'http/protobuf':\n            exporters.push(new OTLPProtoLogExporter());\n            break;\n          case undefined:\n          case '':\n            exporters.push(new OTLPProtoLogExporter());\n            break;\n          default:\n            diag.warn(\n              `Unsupported OTLP logs protocol: \"${protocol}\". Using http/protobuf.`\n            );\n            exporters.push(new OTLPProtoLogExporter());\n        }\n      } else if (exporter === 'console') {\n        exporters.push(new ConsoleLogRecordExporter());\n      } else {\n        diag.warn(\n          `Unsupported OTEL_LOGS_EXPORTER value: \"${exporter}\". Supported values are: otlp, console, none.`\n        );\n      }\n    });\n\n    if (exporters.length > 0) {\n      this._loggerProviderConfig = {\n        logRecordProcessors: exporters.map(exporter => {\n          if (exporter instanceof ConsoleLogRecordExporter) {\n            return new SimpleLogRecordProcessor(exporter);\n          } else {\n            return new BatchLogRecordProcessor(exporter);\n          }\n        }),\n      };\n    }\n  }\n}\n"]}