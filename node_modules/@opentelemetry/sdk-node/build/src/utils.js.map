{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA6D;AAC7D,8CAM6B;AAC7B,wFAAuG;AACvG,sFAAqG;AACrG,sFAAqG;AACrG,oEAAgE;AAChE,wDAOkC;AAClC,kEAMuC;AACvC,gEAA8E;AAC9E,wEAAoE;AAEpE,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,MAAM,sBAAsB,GAAG,MAAM,CAAC;AACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,MAAM,yBAAyB,GAAG,SAAS,CAAC;AAC5C,MAAM,qCAAqC,GAAG,iBAAiB,CAAC;AAEhE,SAAgB,2BAA2B;IACzC,+FAA+F;IAC/F,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAA2B;QAC1D,CAAC,6BAA6B,EAAE,uBAAW,CAAC;QAC5C,CAAC,sBAAsB,EAAE,wBAAY,CAAC;QACtC,CAAC,oBAAoB,EAAE,sBAAU,CAAC;QAClC,CAAC,qCAAqC,EAAE,qCAAyB,CAAC;QAClE,CAAC,yBAAyB,EAAE,2BAAe,CAAC;KAC7C,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAAG,IAAA,2BAAoB,EACnD,8BAA8B,CAC/B,IAAI,CAAC,KAAK,CAAC,CAAC;IAEb,IAAI,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAO,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;KAC/C;IAED,IAAI,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7C,OAAO,EAAE,CAAC;KACX;IAED,OAAO,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACjD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,EAAE;YACrB,UAAI,CAAC,IAAI,CACP,8BAA8B,QAAQ,sEAAsE,CAC7G,CAAC;SACH;QACD,OAAO,gBAAgB,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC;AA/BD,kEA+BC;AAED,SAAgB,oBAAoB,CAAC,IAAc;IACjD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AAC7E,CAAC;AAFD,oDAEC;AAED,SAAgB,sBAAsB;IACpC,OAAO,CACL,IAAA,uBAAgB,EAAC,oCAAoC,CAAC;QACtD,IAAA,uBAAgB,EAAC,6BAA6B,CAAC;QAC/C,eAAe,CAChB,CAAC;AACJ,CAAC;AAND,wDAMC;AAED,SAAS,sBAAsB;IAC7B,MAAM,QAAQ,GAAG,sBAAsB,EAAE,CAAC;IAE1C,QAAQ,QAAQ,EAAE;QAChB,KAAK,MAAM;YACT,OAAO,IAAI,4CAAqB,EAAE,CAAC;QACrC,KAAK,WAAW;YACd,OAAO,IAAI,4CAAqB,EAAE,CAAC;QACrC,KAAK,eAAe;YAClB,OAAO,IAAI,6CAAsB,EAAE,CAAC;QACtC;YACE,UAAI,CAAC,IAAI,CACP,qCAAqC,QAAQ,wBAAwB,CACtE,CAAC;YACF,OAAO,IAAI,6CAAsB,EAAE,CAAC;KACvC;AACH,CAAC;AAED,SAAS,iBAAiB;IACxB,gEAAgE;IAChE,8EAA8E;IAC9E,wDAAwD;IACxD,IAAI;QACF,iEAAiE;QACjE,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACrE,OAAO,IAAI,cAAc,EAAE,CAAC;KAC7B;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,IAAI,KAAK,CACb,oMAAoM,CAAC,EAAE,CACxM,CAAC;KACH;AACH,CAAC;AAED,SAAgB,wBAAwB;IACtC,MAAM,YAAY,GAAG,IAAI,GAAG,CAA6B;QACvD,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,sBAAsB,EAAE,CAAC;QACxC,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,gCAAc,EAAE,CAAC;QACtC,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,oCAAmB,EAAE,CAAC;QAC5C,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;KACtC,CAAC,CAAC;IACH,MAAM,SAAS,GAAmB,EAAE,CAAC;IACrC,MAAM,UAAU,GAAoB,EAAE,CAAC;IACvC,IAAI,kBAAkB,GAAG,oBAAoB,CAC3C,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAA,2BAAoB,EAAC,sBAAsB,CAAC,CAAC,CAAC,CAClE,CAAC;IAEF,IAAI,kBAAkB,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;QACpC,UAAI,CAAC,IAAI,CACP,oEAAoE,CACrE,CAAC;QACF,OAAO,EAAE,CAAC;KACX;IAED,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;QACnC,UAAI,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;QAC1E,kBAAkB,GAAG,CAAC,MAAM,CAAC,CAAC;KAC/B;SAAM,IACL,kBAAkB,CAAC,MAAM,GAAG,CAAC;QAC7B,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,EACnC;QACA,UAAI,CAAC,IAAI,CACP,+FAA+F,CAChG,CAAC;QACF,kBAAkB,GAAG,CAAC,MAAM,CAAC,CAAC;KAC/B;IAED,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE;QACrC,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QAC5C,IAAI,QAAQ,EAAE;YACZ,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC1B;aAAM;YACL,UAAI,CAAC,IAAI,CAAC,4CAA4C,IAAI,GAAG,CAAC,CAAC;SAChE;KACF;IAED,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;QAC3B,IAAI,GAAG,YAAY,oCAAmB,EAAE;YACtC,UAAU,CAAC,IAAI,CAAC,IAAI,oCAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/C;aAAM;YACL,UAAU,CAAC,IAAI,CAAC,IAAI,mCAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9C;KACF;IAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,UAAI,CAAC,IAAI,CACP,oFAAoF,CACrF,CAAC;KACH;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAzDD,4DAyDC;AAED;;GAEG;AACH,SAAgB,oBAAoB;IAClC,6CAA6C;IAC7C,MAAM,sBAAsB,GAAG,IAAA,2BAAoB,EAAC,kBAAkB,CAAC,CAAC;IACxE,IAAI,sBAAsB,IAAI,IAAI,EAAE;QAClC,2CAA2C;QAC3C,OAAO,SAAS,CAAC;KAClB;IAED,oHAAoH;IACpH,wHAAwH;IACxH,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAkC;QAClE,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,gCAAyB,EAAE,CAAC;QACvD,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,2BAAoB,EAAE,CAAC;QAC7C,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,4BAAY,EAAE,CAAC;QAChC;YACE,SAAS;YACT,GAAG,EAAE,CAAC,IAAI,4BAAY,CAAC,EAAE,cAAc,EAAE,gCAAgB,CAAC,YAAY,EAAE,CAAC;SAC1E;QACD,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,oCAAgB,EAAE,CAAC;KACzC,CAAC,CAAC;IAEH,2EAA2E;IAC3E,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC;IAE1E,MAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACnD,MAAM,UAAU,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QACpD,IAAI,CAAC,UAAU,EAAE;YACf,UAAI,CAAC,IAAI,CACP,eAAe,IAAI,0DAA0D,CAC9E,CAAC;YACF,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CACzC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QACb,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;QACD,OAAO,IAAI,CAAC;IACd,CAAC,EACD,EAAE,CACH,CAAC;IAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;QACjC,uEAAuE;QACvE,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE;QAC7C,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;KAC5B;SAAM;QACL,OAAO,IAAI,0BAAmB,CAAC;YAC7B,WAAW,EAAE,gBAAgB;SAC9B,CAAC,CAAC;KACJ;AACH,CAAC;AAxDD,oDAwDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, TextMapPropagator } from '@opentelemetry/api';\nimport {\n  CompositePropagator,\n  getStringFromEnv,\n  getStringListFromEnv,\n  W3CBaggagePropagator,\n  W3CTraceContextPropagator,\n} from '@opentelemetry/core';\nimport { OTLPTraceExporter as OTLPProtoTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';\nimport { OTLPTraceExporter as OTLPHttpTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';\nimport { OTLPTraceExporter as OTLPGrpcTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';\nimport { ZipkinExporter } from '@opentelemetry/exporter-zipkin';\nimport {\n  envDetector,\n  hostDetector,\n  osDetector,\n  processDetector,\n  ResourceDetector,\n  serviceInstanceIdDetector,\n} from '@opentelemetry/resources';\nimport {\n  BatchSpanProcessor,\n  ConsoleSpanExporter,\n  SimpleSpanProcessor,\n  SpanExporter,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport { B3InjectEncoding, B3Propagator } from '@opentelemetry/propagator-b3';\nimport { JaegerPropagator } from '@opentelemetry/propagator-jaeger';\n\nconst RESOURCE_DETECTOR_ENVIRONMENT = 'env';\nconst RESOURCE_DETECTOR_HOST = 'host';\nconst RESOURCE_DETECTOR_OS = 'os';\nconst RESOURCE_DETECTOR_PROCESS = 'process';\nconst RESOURCE_DETECTOR_SERVICE_INSTANCE_ID = 'serviceinstance';\n\nexport function getResourceDetectorsFromEnv(): Array<ResourceDetector> {\n  // When updating this list, make sure to also update the section `resourceDetectors` on README.\n  const resourceDetectors = new Map<string, ResourceDetector>([\n    [RESOURCE_DETECTOR_ENVIRONMENT, envDetector],\n    [RESOURCE_DETECTOR_HOST, hostDetector],\n    [RESOURCE_DETECTOR_OS, osDetector],\n    [RESOURCE_DETECTOR_SERVICE_INSTANCE_ID, serviceInstanceIdDetector],\n    [RESOURCE_DETECTOR_PROCESS, processDetector],\n  ]);\n\n  const resourceDetectorsFromEnv = getStringListFromEnv(\n    'OTEL_NODE_RESOURCE_DETECTORS'\n  ) ?? ['all'];\n\n  if (resourceDetectorsFromEnv.includes('all')) {\n    return [...resourceDetectors.values()].flat();\n  }\n\n  if (resourceDetectorsFromEnv.includes('none')) {\n    return [];\n  }\n\n  return resourceDetectorsFromEnv.flatMap(detector => {\n    const resourceDetector = resourceDetectors.get(detector);\n    if (!resourceDetector) {\n      diag.warn(\n        `Invalid resource detector \"${detector}\" specified in the environment variable OTEL_NODE_RESOURCE_DETECTORS`\n      );\n    }\n    return resourceDetector || [];\n  });\n}\n\nexport function filterBlanksAndNulls(list: string[]): string[] {\n  return list.map(item => item.trim()).filter(s => s !== 'null' && s !== '');\n}\n\nexport function getOtlpProtocolFromEnv(): string {\n  return (\n    getStringFromEnv('OTEL_EXPORTER_OTLP_TRACES_PROTOCOL') ??\n    getStringFromEnv('OTEL_EXPORTER_OTLP_PROTOCOL') ??\n    'http/protobuf'\n  );\n}\n\nfunction getOtlpExporterFromEnv(): SpanExporter {\n  const protocol = getOtlpProtocolFromEnv();\n\n  switch (protocol) {\n    case 'grpc':\n      return new OTLPGrpcTraceExporter();\n    case 'http/json':\n      return new OTLPHttpTraceExporter();\n    case 'http/protobuf':\n      return new OTLPProtoTraceExporter();\n    default:\n      diag.warn(\n        `Unsupported OTLP traces protocol: ${protocol}. Using http/protobuf.`\n      );\n      return new OTLPProtoTraceExporter();\n  }\n}\n\nfunction getJaegerExporter() {\n  // The JaegerExporter does not support being required in bundled\n  // environments. By delaying the require statement to here, we only crash when\n  // the exporter is actually used in such an environment.\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const { JaegerExporter } = require('@opentelemetry/exporter-jaeger');\n    return new JaegerExporter();\n  } catch (e) {\n    throw new Error(\n      `Could not instantiate JaegerExporter. This could be due to the JaegerExporter's lack of support for bundling. If possible, use @opentelemetry/exporter-trace-otlp-proto instead. Original Error: ${e}`\n    );\n  }\n}\n\nexport function getSpanProcessorsFromEnv(): SpanProcessor[] {\n  const exportersMap = new Map<string, () => SpanExporter>([\n    ['otlp', () => getOtlpExporterFromEnv()],\n    ['zipkin', () => new ZipkinExporter()],\n    ['console', () => new ConsoleSpanExporter()],\n    ['jaeger', () => getJaegerExporter()],\n  ]);\n  const exporters: SpanExporter[] = [];\n  const processors: SpanProcessor[] = [];\n  let traceExportersList = filterBlanksAndNulls(\n    Array.from(new Set(getStringListFromEnv('OTEL_TRACES_EXPORTER')))\n  );\n\n  if (traceExportersList[0] === 'none') {\n    diag.warn(\n      'OTEL_TRACES_EXPORTER contains \"none\". SDK will not be initialized.'\n    );\n    return [];\n  }\n\n  if (traceExportersList.length === 0) {\n    diag.debug('OTEL_TRACES_EXPORTER is empty. Using default otlp exporter.');\n    traceExportersList = ['otlp'];\n  } else if (\n    traceExportersList.length > 1 &&\n    traceExportersList.includes('none')\n  ) {\n    diag.warn(\n      'OTEL_TRACES_EXPORTER contains \"none\" along with other exporters. Using default otlp exporter.'\n    );\n    traceExportersList = ['otlp'];\n  }\n\n  for (const name of traceExportersList) {\n    const exporter = exportersMap.get(name)?.();\n    if (exporter) {\n      exporters.push(exporter);\n    } else {\n      diag.warn(`Unrecognized OTEL_TRACES_EXPORTER value: ${name}.`);\n    }\n  }\n\n  for (const exp of exporters) {\n    if (exp instanceof ConsoleSpanExporter) {\n      processors.push(new SimpleSpanProcessor(exp));\n    } else {\n      processors.push(new BatchSpanProcessor(exp));\n    }\n  }\n\n  if (exporters.length === 0) {\n    diag.warn(\n      'Unable to set up trace exporter(s) due to invalid exporter and/or protocol values.'\n    );\n  }\n\n  return processors;\n}\n\n/**\n * Get a propagator as defined by environment variables\n */\nexport function getPropagatorFromEnv(): TextMapPropagator | null | undefined {\n  // Empty and undefined MUST be treated equal.\n  const propagatorsEnvVarValue = getStringListFromEnv('OTEL_PROPAGATORS');\n  if (propagatorsEnvVarValue == null) {\n    // return undefined to fall back to default\n    return undefined;\n  }\n\n  // Implementation note: this only contains specification required propagators that are actually hosted in this repo.\n  // Any other propagators (like aws, aws-lambda, should go into `@opentelemetry/auto-configuration-propagators` instead).\n  const propagatorsFactory = new Map<string, () => TextMapPropagator>([\n    ['tracecontext', () => new W3CTraceContextPropagator()],\n    ['baggage', () => new W3CBaggagePropagator()],\n    ['b3', () => new B3Propagator()],\n    [\n      'b3multi',\n      () => new B3Propagator({ injectEncoding: B3InjectEncoding.MULTI_HEADER }),\n    ],\n    ['jaeger', () => new JaegerPropagator()],\n  ]);\n\n  // Values MUST be deduplicated in order to register a Propagator only once.\n  const uniquePropagatorNames = Array.from(new Set(propagatorsEnvVarValue));\n\n  const propagators = uniquePropagatorNames.map(name => {\n    const propagator = propagatorsFactory.get(name)?.();\n    if (!propagator) {\n      diag.warn(\n        `Propagator \"${name}\" requested through environment variable is unavailable.`\n      );\n      return undefined;\n    }\n\n    return propagator;\n  });\n\n  const validPropagators = propagators.reduce<TextMapPropagator[]>(\n    (list, item) => {\n      if (item) {\n        list.push(item);\n      }\n      return list;\n    },\n    []\n  );\n\n  if (validPropagators.length === 0) {\n    // null to signal that the default should **not** be used in its place.\n    return null;\n  } else if (uniquePropagatorNames.length === 1) {\n    return validPropagators[0];\n  } else {\n    return new CompositePropagator({\n      propagators: validPropagators,\n    });\n  }\n}\n"]}