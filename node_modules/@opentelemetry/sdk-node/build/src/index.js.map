{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2EAA2E;AAC3E,8EAA8E;AAC9E,2EAA2E;AAC3E,0CAA0C;AAE1C,yCAAyC;AACzC,4CAA0C;AAC1C,oDAAkD;AAClD,8CAA4C;AAC5C,kDAAgD;AAChD,wDAAsD;AACtD,wDAAsD;AACtD,wDAAsD;AACtD,2DAAyD;AACzD,wCAAwC;AAExC,6BAAgC;AAAvB,8FAAA,OAAO,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// This is a meta-package, and these exist in to re-export *all* items from\n// the individual packages as individual _namespaces_, so wildcard exports are\n// appropriate here. Otherwise, it'd be a pain to enumerate and keep things\n// in-sync with all the upstream packages.\n\n/* eslint-disable no-restricted-syntax */\nexport * as api from '@opentelemetry/api';\nexport * as contextBase from '@opentelemetry/api';\nexport * as core from '@opentelemetry/core';\nexport * as logs from '@opentelemetry/sdk-logs';\nexport * as metrics from '@opentelemetry/sdk-metrics';\nexport * as node from '@opentelemetry/sdk-trace-node';\nexport * as resources from '@opentelemetry/resources';\nexport * as tracing from '@opentelemetry/sdk-trace-base';\n/* eslint-enable no-restricted-syntax */\n\nexport { NodeSDK } from './sdk';\nexport type { LoggerProviderConfig, MeterProviderConfig } from './sdk';\nexport type { NodeSDKConfiguration } from './types';\n"]}