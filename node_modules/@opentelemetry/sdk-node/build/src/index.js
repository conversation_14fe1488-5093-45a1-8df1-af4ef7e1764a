"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeSDK = exports.tracing = exports.resources = exports.node = exports.metrics = exports.logs = exports.core = exports.contextBase = exports.api = void 0;
// This is a meta-package, and these exist in to re-export *all* items from
// the individual packages as individual _namespaces_, so wildcard exports are
// appropriate here. Otherwise, it'd be a pain to enumerate and keep things
// in-sync with all the upstream packages.
/* eslint-disable no-restricted-syntax */
exports.api = require("@opentelemetry/api");
exports.contextBase = require("@opentelemetry/api");
exports.core = require("@opentelemetry/core");
exports.logs = require("@opentelemetry/sdk-logs");
exports.metrics = require("@opentelemetry/sdk-metrics");
exports.node = require("@opentelemetry/sdk-trace-node");
exports.resources = require("@opentelemetry/resources");
exports.tracing = require("@opentelemetry/sdk-trace-base");
/* eslint-enable no-restricted-syntax */
var sdk_1 = require("./sdk");
Object.defineProperty(exports, "NodeSDK", { enumerable: true, get: function () { return sdk_1.NodeSDK; } });
//# sourceMappingURL=index.js.map