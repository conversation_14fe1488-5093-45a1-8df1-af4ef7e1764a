{"version": 3, "file": "OTLPMetricExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPMetricExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,0FAAmF;AAEnF,sEAA4E;AAC5E,2CAAwC;AACxC,2EAGqD;AAErD,MAAa,kBAAmB,SAAQ,mDAAsB;IAC5D,YAAY,MAA+D;QACzE,KAAK,CACH,IAAA,wCAA4B,EAC1B,IAAA,oCAAwB,EAAC,MAAM,IAAI,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE;YAC9D,YAAY,EAAE,iCAAiC,iBAAO,EAAE;YACxD,cAAc,EAAE,wBAAwB;SACzC,CAAC,EACF,4CAAyB,CAC1B,EACD,MAAM,CACP,CAAC;IACJ,CAAC;CACF;AAbD,gDAaC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPMetricExporterOptions } from '@opentelemetry/exporter-metrics-otlp-http';\nimport { OTLPMetricExporterBase } from '@opentelemetry/exporter-metrics-otlp-http';\nimport { OTLPExporterNodeConfigBase } from '@opentelemetry/otlp-exporter-base';\nimport { ProtobufMetricsSerializer } from '@opentelemetry/otlp-transformer';\nimport { VERSION } from '../../version';\nimport {\n  convertLegacyHttpOptions,\n  createOtlpHttpExportDelegate,\n} from '@opentelemetry/otlp-exporter-base/node-http';\n\nexport class OTLPMetricExporter extends OTLPMetricExporterBase {\n  constructor(config?: OTLPExporterNodeConfigBase & OTLPMetricExporterOptions) {\n    super(\n      createOtlpHttpExportDelegate(\n        convertLegacyHttpOptions(config ?? {}, 'METRICS', 'v1/metrics', {\n          'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n          'Content-Type': 'application/x-protobuf',\n        }),\n        ProtobufMetricsSerializer\n      ),\n      config\n    );\n  }\n}\n"]}