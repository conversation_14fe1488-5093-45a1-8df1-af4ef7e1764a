{"version": 3, "file": "OTLPMetricExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPMetricExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,sBAAsB,EAAE,MAAM,2CAA2C,CAAC;AAEnF,OAAO,EAAE,yBAAyB,EAAE,MAAM,iCAAiC,CAAC;AAC5E,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAEvG,MAAM,OAAO,kBAAmB,SAAQ,sBAAsB;IAC5D,YACE,SAAiE,EAAE;QAEnE,KAAK,CACH,qCAAqC,CACnC,MAAM,EACN,yBAAyB,EACzB,YAAY,EACZ,EAAE,cAAc,EAAE,wBAAwB,EAAE,CAC7C,EACD,MAAM,CACP,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPMetricExporterOptions } from '@opentelemetry/exporter-metrics-otlp-http';\nimport { OTLPMetricExporterBase } from '@opentelemetry/exporter-metrics-otlp-http';\nimport { OTLPExporterNodeConfigBase } from '@opentelemetry/otlp-exporter-base';\nimport { ProtobufMetricsSerializer } from '@opentelemetry/otlp-transformer';\nimport { createLegacyOtlpBrowserExportDelegate } from '@opentelemetry/otlp-exporter-base/browser-http';\n\nexport class OTLPMetricExporter extends OTLPMetricExporterBase {\n  constructor(\n    config: OTLPExporterNodeConfigBase & OTLPMetricExporterOptions = {}\n  ) {\n    super(\n      createLegacyOtlpBrowserExportDelegate(\n        config,\n        ProtobufMetricsSerializer,\n        'v1/metrics',\n        { 'Content-Type': 'application/x-protobuf' }\n      ),\n      config\n    );\n  }\n}\n"]}