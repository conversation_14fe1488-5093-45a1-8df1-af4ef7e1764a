{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../../src/metrics/internal-types.ts"], "names": [], "mappings": ";;;AA8RA;;;;GAIG;AACH,IAAkB,uBAkEjB;AAlED,WAAkB,uBAAuB;IACvC,6EAA6E;IAC7E,mIAAuC,CAAA;IAEvC;;;;;;;;;;;;;;;;;;;;;;;mCAuB+B;IAC/B,uHAAiC,CAAA;IAEjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAkCqC;IACrC,iIAAsC,CAAA;AACxC,CAAC,EAlEiB,uBAAuB,GAAvB,+BAAuB,KAAvB,+BAAuB,QAkExC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Fixed64,\n  IInstrumentationScope,\n  IKeyValue,\n  Resource,\n} from '../common/internal-types';\n\n/** Properties of an ExportMetricsServiceRequest. */\nexport interface IExportMetricsServiceRequest {\n  /** ExportMetricsServiceRequest resourceMetrics */\n  resourceMetrics: IResourceMetrics[];\n}\n\n/** Properties of a ResourceMetrics. */\nexport interface IResourceMetrics {\n  /** ResourceMetrics resource */\n  resource?: Resource;\n\n  /** ResourceMetrics scopeMetrics */\n  scopeMetrics: IScopeMetrics[];\n\n  /** ResourceMetrics schemaUrl */\n  schemaUrl?: string;\n}\n\n/** Properties of an IScopeMetrics. */\nexport interface IScopeMetrics {\n  /** ScopeMetrics scope */\n  scope?: IInstrumentationScope;\n\n  /** ScopeMetrics metrics */\n  metrics: IMetric[];\n\n  /** ScopeMetrics schemaUrl */\n  schemaUrl?: string;\n}\n\n/** Properties of a Metric. */\nexport interface IMetric {\n  /** Metric name */\n  name: string;\n\n  /** Metric description */\n  description?: string;\n\n  /** Metric unit */\n  unit?: string;\n\n  /** Metric gauge */\n  gauge?: IGauge;\n\n  /** Metric sum */\n  sum?: ISum;\n\n  /** Metric histogram */\n  histogram?: IHistogram;\n\n  /** Metric exponentialHistogram */\n  exponentialHistogram?: IExponentialHistogram;\n\n  /** Metric summary */\n  summary?: ISummary;\n}\n\n/** Properties of a Gauge. */\nexport interface IGauge {\n  /** Gauge dataPoints */\n  dataPoints: INumberDataPoint[];\n}\n\n/** Properties of a Sum. */\nexport interface ISum {\n  /** Sum dataPoints */\n  dataPoints: INumberDataPoint[];\n\n  /** Sum aggregationTemporality */\n  aggregationTemporality: EAggregationTemporality;\n\n  /** Sum isMonotonic */\n  isMonotonic?: boolean | null;\n}\n\n/** Properties of a Histogram. */\nexport interface IHistogram {\n  /** Histogram dataPoints */\n  dataPoints: IHistogramDataPoint[];\n\n  /** Histogram aggregationTemporality */\n  aggregationTemporality?: EAggregationTemporality;\n}\n\n/** Properties of an ExponentialHistogram. */\nexport interface IExponentialHistogram {\n  /** ExponentialHistogram dataPoints */\n  dataPoints: IExponentialHistogramDataPoint[];\n\n  /** ExponentialHistogram aggregationTemporality */\n  aggregationTemporality?: EAggregationTemporality;\n}\n\n/** Properties of a Summary. */\nexport interface ISummary {\n  /** Summary dataPoints */\n  dataPoints: ISummaryDataPoint[];\n}\n\n/** Properties of a NumberDataPoint. */\nexport interface INumberDataPoint {\n  /** NumberDataPoint attributes */\n  attributes: IKeyValue[];\n\n  /** NumberDataPoint startTimeUnixNano */\n  startTimeUnixNano?: Fixed64;\n\n  /** NumberDataPoint timeUnixNano */\n  timeUnixNano?: Fixed64;\n\n  /** NumberDataPoint asDouble */\n  asDouble?: number | null;\n\n  /** NumberDataPoint asInt */\n  asInt?: number;\n\n  /** NumberDataPoint exemplars */\n  exemplars?: IExemplar[];\n\n  /** NumberDataPoint flags */\n  flags?: number;\n}\n\n/** Properties of a HistogramDataPoint. */\nexport interface IHistogramDataPoint {\n  /** HistogramDataPoint attributes */\n  attributes?: IKeyValue[];\n\n  /** HistogramDataPoint startTimeUnixNano */\n  startTimeUnixNano?: Fixed64;\n\n  /** HistogramDataPoint timeUnixNano */\n  timeUnixNano?: Fixed64;\n\n  /** HistogramDataPoint count */\n  count?: number;\n\n  /** HistogramDataPoint sum */\n  sum?: number;\n\n  /** HistogramDataPoint bucketCounts */\n  bucketCounts?: number[];\n\n  /** HistogramDataPoint explicitBounds */\n  explicitBounds?: number[];\n\n  /** HistogramDataPoint exemplars */\n  exemplars?: IExemplar[];\n\n  /** HistogramDataPoint flags */\n  flags?: number;\n\n  /** HistogramDataPoint min */\n  min?: number;\n\n  /** HistogramDataPoint max */\n  max?: number;\n}\n\n/** Properties of an ExponentialHistogramDataPoint. */\nexport interface IExponentialHistogramDataPoint {\n  /** ExponentialHistogramDataPoint attributes */\n  attributes?: IKeyValue[];\n\n  /** ExponentialHistogramDataPoint startTimeUnixNano */\n  startTimeUnixNano?: Fixed64;\n\n  /** ExponentialHistogramDataPoint timeUnixNano */\n  timeUnixNano?: Fixed64;\n\n  /** ExponentialHistogramDataPoint count */\n  count?: number;\n\n  /** ExponentialHistogramDataPoint sum */\n  sum?: number;\n\n  /** ExponentialHistogramDataPoint scale */\n  scale?: number;\n\n  /** ExponentialHistogramDataPoint zeroCount */\n  zeroCount?: number;\n\n  /** ExponentialHistogramDataPoint positive */\n  positive?: IBuckets;\n\n  /** ExponentialHistogramDataPoint negative */\n  negative?: IBuckets;\n\n  /** ExponentialHistogramDataPoint flags */\n  flags?: number;\n\n  /** ExponentialHistogramDataPoint exemplars */\n  exemplars?: IExemplar[];\n\n  /** ExponentialHistogramDataPoint min */\n  min?: number;\n\n  /** ExponentialHistogramDataPoint max */\n  max?: number;\n}\n\n/** Properties of a SummaryDataPoint. */\nexport interface ISummaryDataPoint {\n  /** SummaryDataPoint attributes */\n  attributes?: IKeyValue[];\n\n  /** SummaryDataPoint startTimeUnixNano */\n  startTimeUnixNano?: number;\n\n  /** SummaryDataPoint timeUnixNano */\n  timeUnixNano?: string;\n\n  /** SummaryDataPoint count */\n  count?: number;\n\n  /** SummaryDataPoint sum */\n  sum?: number;\n\n  /** SummaryDataPoint quantileValues */\n  quantileValues?: IValueAtQuantile[];\n\n  /** SummaryDataPoint flags */\n  flags?: number;\n}\n\n/** Properties of a ValueAtQuantile. */\nexport interface IValueAtQuantile {\n  /** ValueAtQuantile quantile */\n  quantile?: number;\n\n  /** ValueAtQuantile value */\n  value?: number;\n}\n\n/** Properties of a Buckets. */\nexport interface IBuckets {\n  /** Buckets offset */\n  offset?: number;\n\n  /** Buckets bucketCounts */\n  bucketCounts?: number[];\n}\n\n/** Properties of an Exemplar. */\nexport interface IExemplar {\n  /** Exemplar filteredAttributes */\n  filteredAttributes?: IKeyValue[];\n\n  /** Exemplar timeUnixNano */\n  timeUnixNano?: string;\n\n  /** Exemplar asDouble */\n  asDouble?: number;\n\n  /** Exemplar asInt */\n  asInt?: number;\n\n  /** Exemplar spanId */\n  spanId?: string | Uint8Array;\n\n  /** Exemplar traceId */\n  traceId?: string | Uint8Array;\n}\n\n/**\n * AggregationTemporality defines how a metric aggregator reports aggregated\n * values. It describes how those values relate to the time interval over\n * which they are aggregated.\n */\nexport const enum EAggregationTemporality {\n  /* UNSPECIFIED is the default AggregationTemporality, it MUST not be used. */\n  AGGREGATION_TEMPORALITY_UNSPECIFIED = 0,\n\n  /** DELTA is an AggregationTemporality for a metric aggregator which reports\n  changes since last report time. Successive metrics contain aggregation of\n  values from continuous and non-overlapping intervals.\n\n  The values for a DELTA metric are based only on the time interval\n  associated with one measurement cycle. There is no dependency on\n  previous measurements like is the case for CUMULATIVE metrics.\n\n  For example, consider a system measuring the number of requests that\n  it receives and reports the sum of these requests every second as a\n  DELTA metric:\n\n  1. The system starts receiving at time=t_0.\n  2. A request is received, the system measures 1 request.\n  3. A request is received, the system measures 1 request.\n  4. A request is received, the system measures 1 request.\n  5. The 1 second collection cycle ends. A metric is exported for the\n      number of requests received over the interval of time t_0 to\n      t_0+1 with a value of 3.\n  6. A request is received, the system measures 1 request.\n  7. A request is received, the system measures 1 request.\n  8. The 1 second collection cycle ends. A metric is exported for the\n      number of requests received over the interval of time t_0+1 to\n      t_0+2 with a value of 2. */\n  AGGREGATION_TEMPORALITY_DELTA = 1,\n\n  /** CUMULATIVE is an AggregationTemporality for a metric aggregator which\n  reports changes since a fixed start time. This means that current values\n  of a CUMULATIVE metric depend on all previous measurements since the\n  start time. Because of this, the sender is required to retain this state\n  in some form. If this state is lost or invalidated, the CUMULATIVE metric\n  values MUST be reset and a new fixed start time following the last\n  reported measurement time sent MUST be used.\n\n  For example, consider a system measuring the number of requests that\n  it receives and reports the sum of these requests every second as a\n  CUMULATIVE metric:\n\n  1. The system starts receiving at time=t_0.\n  2. A request is received, the system measures 1 request.\n  3. A request is received, the system measures 1 request.\n  4. A request is received, the system measures 1 request.\n  5. The 1 second collection cycle ends. A metric is exported for the\n      number of requests received over the interval of time t_0 to\n      t_0+1 with a value of 3.\n  6. A request is received, the system measures 1 request.\n  7. A request is received, the system measures 1 request.\n  8. The 1 second collection cycle ends. A metric is exported for the\n      number of requests received over the interval of time t_0 to\n      t_0+2 with a value of 5.\n  9. The system experiences a fault and loses state.\n  10. The system recovers and resumes receiving at time=t_1.\n  11. A request is received, the system measures 1 request.\n  12. The 1 second collection cycle ends. A metric is exported for the\n      number of requests received over the interval of time t_1 to\n      t_0+1 with a value of 1.\n\n  Note: Even though, when reporting changes since last report time, using\n  CUMULATIVE is valid, it is not recommended. This may cause problems for\n  systems that do not use start_time to determine when the aggregation\n  value was reset (e.g. Prometheus). */\n  AGGREGATION_TEMPORALITY_CUMULATIVE = 2,\n}\n"]}