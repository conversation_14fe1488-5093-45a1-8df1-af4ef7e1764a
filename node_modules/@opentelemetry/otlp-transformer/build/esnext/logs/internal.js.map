{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/logs/internal.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAUH,OAAO,EAAW,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAC1D,OAAO,EACL,0BAA0B,EAC1B,cAAc,EACd,UAAU,EACV,UAAU,GACX,MAAM,oBAAoB,CAAC;AAK5B,MAAM,UAAU,8BAA8B,CAC5C,UAA+B,EAC/B,OAA6B;IAE7B,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IACxC,OAAO;QACL,YAAY,EAAE,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC;KAC5D,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,UAA+B;IAE/B,MAAM,WAAW,GAGb,IAAI,GAAG,EAAE,CAAC;IAEd,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;QAC/B,MAAM,EACJ,QAAQ,EACR,oBAAoB,EAAE,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,GAC7D,GAAG,MAAM,CAAC;QAEX,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACnB,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SACnC;QAED,MAAM,MAAM,GAAG,GAAG,IAAI,IAAI,OAAO,IAAI,SAAS,EAAE,CAAC;QACjD,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAC7B;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtB;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAA+B,EAC/B,OAAgB;IAEhB,MAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAClD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QACtD,QAAQ,EAAE,cAAc,CAAC,QAAQ,CAAC;QAClC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE;YAC9C,OAAO;gBACL,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;gBACpE,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBAC3D,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,SAAS;aACvD,CAAC;QACJ,CAAC,CAAC;QACF,SAAS,EAAE,SAAS;KACrB,CAAC,CAAC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAAC,GAAsB,EAAE,OAAgB;IAC3D,OAAO;QACL,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;QAC9C,oBAAoB,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC;QAC9D,cAAc,EAAE,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC;QACpD,YAAY,EAAE,GAAG,CAAC,YAAY;QAC9B,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1B,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,UAAU,EAAE,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC;QAC3C,sBAAsB,EAAE,GAAG,CAAC,sBAAsB;QAClD,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,UAAU;QAClC,OAAO,EAAE,OAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;QACpE,MAAM,EAAE,OAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC;KACnE,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CACvB,cAA0C;IAE1C,OAAO,cAAmE,CAAC;AAC7E,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,UAAyB;IACvD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ReadableLogRecord } from '@opentelemetry/sdk-logs';\nimport {\n  ESeverityNumber,\n  IExportLogsServiceRequest,\n  ILogRecord,\n  IResourceLogs,\n} from './internal-types';\nimport { Resource } from '@opentelemetry/resources';\nimport { Encoder, getOtlpEncoder } from '../common/utils';\nimport {\n  createInstrumentationScope,\n  createResource,\n  toAnyValue,\n  toKeyValue,\n} from '../common/internal';\nimport { SeverityNumber } from '@opentelemetry/api-logs';\nimport { OtlpEncodingOptions, IKeyValue } from '../common/internal-types';\nimport { LogAttributes } from '@opentelemetry/api-logs';\n\nexport function createExportLogsServiceRequest(\n  logRecords: ReadableLogRecord[],\n  options?: OtlpEncodingOptions\n): IExportLogsServiceRequest {\n  const encoder = getOtlpEncoder(options);\n  return {\n    resourceLogs: logRecordsToResourceLogs(logRecords, encoder),\n  };\n}\n\nfunction createResourceMap(\n  logRecords: ReadableLogRecord[]\n): Map<Resource, Map<string, ReadableLogRecord[]>> {\n  const resourceMap: Map<\n    Resource,\n    Map<string, ReadableLogRecord[]>\n  > = new Map();\n\n  for (const record of logRecords) {\n    const {\n      resource,\n      instrumentationScope: { name, version = '', schemaUrl = '' },\n    } = record;\n\n    let ismMap = resourceMap.get(resource);\n    if (!ismMap) {\n      ismMap = new Map();\n      resourceMap.set(resource, ismMap);\n    }\n\n    const ismKey = `${name}@${version}:${schemaUrl}`;\n    let records = ismMap.get(ismKey);\n    if (!records) {\n      records = [];\n      ismMap.set(ismKey, records);\n    }\n    records.push(record);\n  }\n  return resourceMap;\n}\n\nfunction logRecordsToResourceLogs(\n  logRecords: ReadableLogRecord[],\n  encoder: Encoder\n): IResourceLogs[] {\n  const resourceMap = createResourceMap(logRecords);\n  return Array.from(resourceMap, ([resource, ismMap]) => ({\n    resource: createResource(resource),\n    scopeLogs: Array.from(ismMap, ([, scopeLogs]) => {\n      return {\n        scope: createInstrumentationScope(scopeLogs[0].instrumentationScope),\n        logRecords: scopeLogs.map(log => toLogRecord(log, encoder)),\n        schemaUrl: scopeLogs[0].instrumentationScope.schemaUrl,\n      };\n    }),\n    schemaUrl: undefined,\n  }));\n}\n\nfunction toLogRecord(log: ReadableLogRecord, encoder: Encoder): ILogRecord {\n  return {\n    timeUnixNano: encoder.encodeHrTime(log.hrTime),\n    observedTimeUnixNano: encoder.encodeHrTime(log.hrTimeObserved),\n    severityNumber: toSeverityNumber(log.severityNumber),\n    severityText: log.severityText,\n    body: toAnyValue(log.body),\n    eventName: log.eventName,\n    attributes: toLogAttributes(log.attributes),\n    droppedAttributesCount: log.droppedAttributesCount,\n    flags: log.spanContext?.traceFlags,\n    traceId: encoder.encodeOptionalSpanContext(log.spanContext?.traceId),\n    spanId: encoder.encodeOptionalSpanContext(log.spanContext?.spanId),\n  };\n}\n\nfunction toSeverityNumber(\n  severityNumber: SeverityNumber | undefined\n): ESeverityNumber | undefined {\n  return severityNumber as number | undefined as ESeverityNumber | undefined;\n}\n\nexport function toLogAttributes(attributes: LogAttributes): IKeyValue[] {\n  return Object.keys(attributes).map(key => toKeyValue(key, attributes[key]));\n}\n"]}