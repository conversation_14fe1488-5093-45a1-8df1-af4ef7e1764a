/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Numerical value of the severity, normalized to values described in Log Data Model.
 */
export var ESeverityNumber;
(function (ESeverityNumber) {
    /** Unspecified. Do NOT use as default */
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_UNSPECIFIED"] = 0] = "SEVERITY_NUMBER_UNSPECIFIED";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_TRACE"] = 1] = "SEVERITY_NUMBER_TRACE";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_TRACE2"] = 2] = "SEVERITY_NUMBER_TRACE2";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_TRACE3"] = 3] = "SEVERITY_NUMBER_TRACE3";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_TRACE4"] = 4] = "SEVERITY_NUMBER_TRACE4";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_DEBUG"] = 5] = "SEVERITY_NUMBER_DEBUG";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_DEBUG2"] = 6] = "SEVERITY_NUMBER_DEBUG2";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_DEBUG3"] = 7] = "SEVERITY_NUMBER_DEBUG3";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_DEBUG4"] = 8] = "SEVERITY_NUMBER_DEBUG4";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_INFO"] = 9] = "SEVERITY_NUMBER_INFO";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_INFO2"] = 10] = "SEVERITY_NUMBER_INFO2";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_INFO3"] = 11] = "SEVERITY_NUMBER_INFO3";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_INFO4"] = 12] = "SEVERITY_NUMBER_INFO4";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_WARN"] = 13] = "SEVERITY_NUMBER_WARN";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_WARN2"] = 14] = "SEVERITY_NUMBER_WARN2";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_WARN3"] = 15] = "SEVERITY_NUMBER_WARN3";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_WARN4"] = 16] = "SEVERITY_NUMBER_WARN4";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_ERROR"] = 17] = "SEVERITY_NUMBER_ERROR";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_ERROR2"] = 18] = "SEVERITY_NUMBER_ERROR2";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_ERROR3"] = 19] = "SEVERITY_NUMBER_ERROR3";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_ERROR4"] = 20] = "SEVERITY_NUMBER_ERROR4";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_FATAL"] = 21] = "SEVERITY_NUMBER_FATAL";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_FATAL2"] = 22] = "SEVERITY_NUMBER_FATAL2";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_FATAL3"] = 23] = "SEVERITY_NUMBER_FATAL3";
    ESeverityNumber[ESeverityNumber["SEVERITY_NUMBER_FATAL4"] = 24] = "SEVERITY_NUMBER_FATAL4";
})(ESeverityNumber || (ESeverityNumber = {}));
//# sourceMappingURL=internal-types.js.map