{"version": 3, "file": "metrics.js", "sourceRoot": "", "sources": ["../../../../src/metrics/json/metrics.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,iCAAiC,EAAE,MAAM,aAAa,CAAC;AAGhE,MAAM,CAAC,MAAM,qBAAqB,GAG9B;IACF,gBAAgB,EAAE,CAAC,GAAoB,EAAE,EAAE;QACzC,MAAM,OAAO,GAAG,iCAAiC,CAAC,CAAC,GAAG,CAAC,EAAE;YACvD,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,mBAAmB,EAAE,CAAC,GAAe,EAAE,EAAE;QACvC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,EAAE,CAAC;SACX;QACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAkC,CAAC;IAC1E,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ISerializer } from '../../i-serializer';\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport { createExportMetricsServiceRequest } from '../internal';\nimport { IExportMetricsServiceResponse } from '../export-response';\n\nexport const JsonMetricsSerializer: ISerializer<\n  ResourceMetrics,\n  IExportMetricsServiceResponse\n> = {\n  serializeRequest: (arg: ResourceMetrics) => {\n    const request = createExportMetricsServiceRequest([arg], {\n      useLongBits: false,\n    });\n    const encoder = new TextEncoder();\n    return encoder.encode(JSON.stringify(request));\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    if (arg.length === 0) {\n      return {};\n    }\n    const decoder = new TextDecoder();\n    return JSON.parse(decoder.decode(arg)) as IExportMetricsServiceResponse;\n  },\n};\n"]}