{"name": "@opentelemetry/sdk-metrics", "version": "2.0.1", "description": "OpenTelemetry metrics SDK", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc mocha 'test/**/*.test.ts'", "test:browser": "karma start --single-run", "tdd": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../scripts/version-update.js", "peer-api-check": "node ../../scripts/peer-api-check.js", "align-api-deps": "node ../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "metrics", "stats", "profiling"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": "^18.19.0 || >=20.6.0"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/preset-env": "7.26.9", "@opentelemetry/api": ">=1.3.0 <1.10.0", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/sinon": "17.0.4", "babel-plugin-istanbul": "7.0.0", "cross-var": "1.1.0", "karma": "6.4.4", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "mocha": "11.1.0", "nyc": "17.1.0", "sinon": "15.1.2", "ts-loader": "9.5.2", "typescript": "5.0.4", "webpack": "5.99.8", "webpack-cli": "6.0.1"}, "peerDependencies": {"@opentelemetry/api": ">=1.9.0 <1.10.0"}, "dependencies": {"@opentelemetry/core": "2.0.1", "@opentelemetry/resources": "2.0.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/sdk-metrics", "sideEffects": false, "gitHead": "4ce5bd165195870f292fa95e312cffe05eb9e09d"}