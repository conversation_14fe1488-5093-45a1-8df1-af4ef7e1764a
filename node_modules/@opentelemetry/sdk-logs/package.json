{"name": "@opentelemetry/sdk-logs", "version": "0.203.0", "publishConfig": {"access": "public"}, "description": "OpenTelemetry logs SDK", "author": "OpenTelemetry Authors", "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/sdk-logs", "license": "Apache-2.0", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "browser": {"./src/platform/index.ts": "./src/platform/browser/index.ts", "./build/esm/platform/index.js": "./build/esm/platform/browser/index.js", "./build/esnext/platform/index.js": "./build/esnext/platform/browser/index.js", "./build/src/platform/index.js": "./build/src/platform/browser/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/open-telemetry/opentelemetry-js.git"}, "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js/issues"}, "engines": {"node": "^18.19.0 || >=20.6.0"}, "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc mocha 'test/**/*.test.ts'", "test:browser": "karma start --single-run", "tdd": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../../scripts/version-update.js", "peer-api-check": "node ../../../scripts/peer-api-check.js", "align-api-deps": "node ../../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "logs", "stats", "profiling"], "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "sideEffects": false, "peerDependencies": {"@opentelemetry/api": ">=1.4.0 <1.10.0"}, "devDependencies": {"@babel/core": "7.27.1", "@babel/preset-env": "7.27.2", "@opentelemetry/api": ">=1.4.0 <1.10.0", "@opentelemetry/resources_1.9.0": "npm:@opentelemetry/resources@1.9.0", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/sinon": "17.0.4", "babel-plugin-istanbul": "7.0.0", "cross-var": "1.1.0", "karma": "6.4.4", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "mocha": "11.1.0", "nyc": "17.1.0", "sinon": "18.0.1", "ts-loader": "9.5.2", "typescript": "5.0.4", "webpack": "5.99.9", "webpack-cli": "6.0.1"}, "dependencies": {"@opentelemetry/api-logs": "0.203.0", "@opentelemetry/core": "2.0.1", "@opentelemetry/resources": "2.0.1"}, "gitHead": "93187f022457da152becc03dd00db8b2500702db"}