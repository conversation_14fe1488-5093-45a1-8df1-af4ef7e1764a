{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAQH,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAIlD,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AACzE,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,yBAAyB,EAAE,MAAM,oCAAoC,CAAC;AAC/E,OAAO,EAAE,uBAAuB,EAAE,MAAM,YAAY,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type {\n  LoggerProviderConfig,\n  LogRecordLimits,\n  BufferConfig,\n  BatchLogRecordProcessorBrowserConfig,\n} from './types';\nexport { LoggerProvider } from './LoggerProvider';\nexport type { SdkLogRecord } from './export/SdkLogRecord';\nexport type { LogRecordProcessor } from './LogRecordProcessor';\nexport type { ReadableLogRecord } from './export/ReadableLogRecord';\nexport { NoopLogRecordProcessor } from './export/NoopLogRecordProcessor';\nexport { ConsoleLogRecordExporter } from './export/ConsoleLogRecordExporter';\nexport type { LogRecordExporter } from './export/LogRecordExporter';\nexport { SimpleLogRecordProcessor } from './export/SimpleLogRecordProcessor';\nexport { InMemoryLogRecordExporter } from './export/InMemoryLogRecordExporter';\nexport { BatchLogRecordProcessor } from './platform';\n"]}