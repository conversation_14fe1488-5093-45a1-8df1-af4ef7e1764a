{"version": 3, "file": "SdkLogRecord.js", "sourceRoot": "", "sources": ["../../../src/export/SdkLogRecord.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { HrTime, SpanContext } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport type {\n  AnyValue,\n  LogBody,\n  LogAttributes,\n  SeverityNumber,\n} from '@opentelemetry/api-logs';\nimport type { Resource } from '@opentelemetry/resources';\n\n/**\n * A recording of a event. Typically the record includes a timestamp indicating when the\n * event happened as well as other data that describes what happened, where it happened, etc.\n *\n * @remarks\n * This interface is **not intended to be implemented by users**.\n * To produce logs, use {@link Logger#emit}. To consume logs, implement {@link LogRecordProcessor#onEmit}.\n * SdkLogRecord instances are created and managed by the SDK.\n */\nexport interface SdkLogRecord {\n  readonly hrTime: HrTime;\n  readonly hrTimeObserved: HrTime;\n  readonly spanContext?: SpanContext;\n  readonly resource: Resource;\n  readonly instrumentationScope: InstrumentationScope;\n  readonly attributes: LogAttributes;\n  severityText?: string;\n  severityNumber?: SeverityNumber;\n  body?: LogBody;\n  eventName?: string;\n  droppedAttributesCount: number;\n\n  /**\n   * Sets a single attribute on the log record.\n   * @param key The attribute key.\n   * @param value The attribute value.\n   * @returns The updated SdkLogRecord.\n   */\n  setAttribute(key: string, value?: AnyValue): SdkLogRecord;\n\n  /**\n   * Sets multiple attributes on the log record.\n   * @param attributes The attributes to set.\n   * @returns The updated SdkLogRecord.\n   */\n  setAttributes(attributes: LogAttributes): SdkLogRecord;\n\n  /**\n   * Sets the body of the log record.\n   * @param body The log body.\n   * @returns The updated SdkLogRecord.\n   */\n  setBody(body: LogBody): SdkLogRecord;\n\n  /**\n   * Sets the event name for the log record.\n   * @param eventName The event name.\n   * @returns The updated SdkLogRecord.\n   */\n  setEventName(eventName: string): SdkLogRecord;\n\n  /**\n   * Sets the severity number for the log record.\n   * @param severityNumber The severity number.\n   * @returns The updated SdkLogRecord.\n   */\n  setSeverityNumber(severityNumber: SeverityNumber): SdkLogRecord;\n\n  /**\n   * Sets the severity text (log level) for the log record.\n   * @param severityText The severity text.\n   * @returns The updated SdkLogRecord.\n   */\n  setSeverityText(severityText: string): SdkLogRecord;\n}\n"]}