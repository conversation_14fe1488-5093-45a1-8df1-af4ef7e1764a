{"version": 3, "file": "ReadableLogRecord.js", "sourceRoot": "", "sources": ["../../../src/export/ReadableLogRecord.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Resource } from '@opentelemetry/resources';\nimport type { HrTime, SpanContext } from '@opentelemetry/api';\nimport type { InstrumentationScope } from '@opentelemetry/core';\nimport type {\n  LogBody,\n  LogAttributes,\n  SeverityNumber,\n} from '@opentelemetry/api-logs';\n\nexport interface ReadableLogRecord {\n  readonly hrTime: HrTime;\n  readonly hrTimeObserved: HrTime;\n  readonly spanContext?: SpanContext;\n  readonly severityText?: string;\n  readonly severityNumber?: SeverityNumber;\n  readonly body?: LogBody;\n  readonly eventName?: string;\n  readonly resource: Resource;\n  readonly instrumentationScope: InstrumentationScope;\n  readonly attributes: LogAttributes;\n  readonly droppedAttributesCount: number;\n}\n"]}