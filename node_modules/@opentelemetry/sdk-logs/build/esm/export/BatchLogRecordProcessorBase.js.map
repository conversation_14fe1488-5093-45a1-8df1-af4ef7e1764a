{"version": 3, "file": "BatchLogRecordProcessorBase.js", "sourceRoot": "", "sources": ["../../../src/export/BatchLogRecordProcessorBase.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAgB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,EACV,cAAc,EACd,QAAQ,EACR,eAAe,GAChB,MAAM,qBAAqB,CAAC;AAO7B,MAAM,OAAgB,2BAA2B;IAa5B;IAVF,mBAAmB,CAAS;IAC5B,aAAa,CAAS;IACtB,qBAAqB,CAAS;IAC9B,oBAAoB,CAAS;IAEtC,mBAAmB,GAAmB,EAAE,CAAC;IACzC,MAAM,CAA6B;IACnC,aAAa,CAAuB;IAE5C,YACmB,SAA4B,EAC7C,MAAU;QADO,cAAS,GAAT,SAAS,CAAmB;QAG7C,IAAI,CAAC,mBAAmB;YACtB,MAAM,EAAE,kBAAkB;gBAC1B,gBAAgB,CAAC,iCAAiC,CAAC;gBACnD,GAAG,CAAC;QACN,IAAI,CAAC,aAAa;YAChB,MAAM,EAAE,YAAY;gBACpB,gBAAgB,CAAC,0BAA0B,CAAC;gBAC5C,IAAI,CAAC;QACP,IAAI,CAAC,qBAAqB;YACxB,MAAM,EAAE,oBAAoB;gBAC5B,gBAAgB,CAAC,0BAA0B,CAAC;gBAC5C,IAAI,CAAC;QACP,IAAI,CAAC,oBAAoB;YACvB,MAAM,EAAE,mBAAmB;gBAC3B,gBAAgB,CAAC,0BAA0B,CAAC;gBAC5C,KAAK,CAAC;QAER,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE;YACjD,IAAI,CAAC,IAAI,CACP,wIAAwI,CACzI,CAAC;YACF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;SAC/C;IACH,CAAC;IAEM,MAAM,CAAC,SAAuB;QACnC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAEM,UAAU;QACf,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACvB,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,qCAAqC;IAC7B,YAAY,CAAC,SAAuB;QAC1C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;YACzD,OAAO;SACR;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;;;SAIK;IACG,SAAS;QACf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAC3D,CAAC;YACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACtC;YACD,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAClB,IAAI,CAAC,GAAG,EAAE;gBACT,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACzC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,eAAe,CACb,IAAI,CAAC,OAAO,CACV,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAC7D,EACD,IAAI,CAAC,oBAAoB,CAC1B;iBACE,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;iBACrB,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,cAAc,EAAE;iBAClB,IAAI,CAAC,GAAG,EAAE;gBACT,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,CAAC,EAAE;gBACT,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEO,WAAW;QACjB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;IACH,CAAC;IAEO,OAAO,CAAC,UAA0B;QACxC,MAAM,QAAQ,GAAG,GAAG,EAAE,CACpB,QAAQ;aACL,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC;aACnC,IAAI,CAAC,CAAC,MAAoB,EAAE,EAAE;YAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;gBAC5C,kBAAkB,CAChB,MAAM,CAAC,KAAK;oBACV,IAAI,KAAK,CACP,6DAA6D,MAAM,GAAG,CACvE,CACJ,CAAC;aACH;QACH,CAAC,CAAC;aACD,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAE/B,MAAM,gBAAgB,GAAG,UAAU;aAChC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;aACpC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QAEvD,sFAAsF;QACtF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO,QAAQ,EAAE,CAAC;SACnB;aAAM;YACL,OAAO,OAAO,CAAC,GAAG,CAChB,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,sBAAsB,EAAE,EAAE,CAAC,CACtE,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;SACtC;IACH,CAAC;CAGF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult, getNumberFromEnv } from '@opentelemetry/core';\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResultCode,\n  globalErrorHandler,\n  unrefTimer,\n  BindOnceFuture,\n  internal,\n  callWithTimeout,\n} from '@opentelemetry/core';\n\nimport type { BufferConfig } from '../types';\nimport type { SdkLogRecord } from './SdkLogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\nimport type { LogRecordProcessor } from '../LogRecordProcessor';\n\nexport abstract class BatchLogRecordProcessorBase<T extends BufferConfig>\n  implements LogRecordProcessor\n{\n  private readonly _maxExportBatchSize: number;\n  private readonly _maxQueueSize: number;\n  private readonly _scheduledDelayMillis: number;\n  private readonly _exportTimeoutMillis: number;\n\n  private _finishedLogRecords: SdkLogRecord[] = [];\n  private _timer: NodeJS.Timeout | undefined;\n  private _shutdownOnce: BindOnceFuture<void>;\n\n  constructor(\n    private readonly _exporter: LogRecordExporter,\n    config?: T\n  ) {\n    this._maxExportBatchSize =\n      config?.maxExportBatchSize ??\n      getNumberFromEnv('OTEL_BLRP_MAX_EXPORT_BATCH_SIZE') ??\n      512;\n    this._maxQueueSize =\n      config?.maxQueueSize ??\n      getNumberFromEnv('OTEL_BLRP_MAX_QUEUE_SIZE') ??\n      2048;\n    this._scheduledDelayMillis =\n      config?.scheduledDelayMillis ??\n      getNumberFromEnv('OTEL_BLRP_SCHEDULE_DELAY') ??\n      5000;\n    this._exportTimeoutMillis =\n      config?.exportTimeoutMillis ??\n      getNumberFromEnv('OTEL_BLRP_EXPORT_TIMEOUT') ??\n      30000;\n\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n\n    if (this._maxExportBatchSize > this._maxQueueSize) {\n      diag.warn(\n        'BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize'\n      );\n      this._maxExportBatchSize = this._maxQueueSize;\n    }\n  }\n\n  public onEmit(logRecord: SdkLogRecord): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n    this._addToBuffer(logRecord);\n  }\n\n  public forceFlush(): Promise<void> {\n    if (this._shutdownOnce.isCalled) {\n      return this._shutdownOnce.promise;\n    }\n    return this._flushAll();\n  }\n\n  public shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private async _shutdown(): Promise<void> {\n    this.onShutdown();\n    await this._flushAll();\n    await this._exporter.shutdown();\n  }\n\n  /** Add a LogRecord in the buffer. */\n  private _addToBuffer(logRecord: SdkLogRecord) {\n    if (this._finishedLogRecords.length >= this._maxQueueSize) {\n      return;\n    }\n    this._finishedLogRecords.push(logRecord);\n    this._maybeStartTimer();\n  }\n\n  /**\n   * Send all LogRecords to the exporter respecting the batch size limit\n   * This function is used only on forceFlush or shutdown,\n   * for all other cases _flush should be used\n   * */\n  private _flushAll(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const promises = [];\n      const batchCount = Math.ceil(\n        this._finishedLogRecords.length / this._maxExportBatchSize\n      );\n      for (let i = 0; i < batchCount; i++) {\n        promises.push(this._flushOneBatch());\n      }\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(reject);\n    });\n  }\n\n  private _flushOneBatch(): Promise<void> {\n    this._clearTimer();\n    if (this._finishedLogRecords.length === 0) {\n      return Promise.resolve();\n    }\n    return new Promise((resolve, reject) => {\n      callWithTimeout(\n        this._export(\n          this._finishedLogRecords.splice(0, this._maxExportBatchSize)\n        ),\n        this._exportTimeoutMillis\n      )\n        .then(() => resolve())\n        .catch(reject);\n    });\n  }\n\n  private _maybeStartTimer() {\n    if (this._timer !== undefined) {\n      return;\n    }\n    this._timer = setTimeout(() => {\n      this._flushOneBatch()\n        .then(() => {\n          if (this._finishedLogRecords.length > 0) {\n            this._clearTimer();\n            this._maybeStartTimer();\n          }\n        })\n        .catch(e => {\n          globalErrorHandler(e);\n        });\n    }, this._scheduledDelayMillis);\n    unrefTimer(this._timer);\n  }\n\n  private _clearTimer() {\n    if (this._timer !== undefined) {\n      clearTimeout(this._timer);\n      this._timer = undefined;\n    }\n  }\n\n  private _export(logRecords: SdkLogRecord[]): Promise<void> {\n    const doExport = () =>\n      internal\n        ._export(this._exporter, logRecords)\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `BatchLogRecordProcessor: log record export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(globalErrorHandler);\n\n    const pendingResources = logRecords\n      .map(logRecord => logRecord.resource)\n      .filter(resource => resource.asyncAttributesPending);\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (pendingResources.length === 0) {\n      return doExport();\n    } else {\n      return Promise.all(\n        pendingResources.map(resource => resource.waitForAsyncAttributes?.())\n      ).then(doExport, globalErrorHandler);\n    }\n  }\n\n  protected abstract onShutdown(): void;\n}\n"]}