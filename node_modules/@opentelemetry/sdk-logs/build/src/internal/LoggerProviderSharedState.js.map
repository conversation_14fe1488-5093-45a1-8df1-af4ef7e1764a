{"version": 3, "file": "LoggerProviderSharedState.js", "sourceRoot": "", "sources": ["../../../src/internal/LoggerProviderSharedState.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,6EAA0E;AAC1E,wEAAqE;AAErE,MAAa,yBAAyB;IAMzB;IACA;IACA;IACA;IARF,OAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;IAClD,eAAe,CAAqB;IAC3B,6BAA6B,GAAyB,EAAE,CAAC;IAElE,YACW,QAAkB,EAClB,uBAA+B,EAC/B,eAA0C,EAC1C,UAAgC;QAHhC,aAAQ,GAAR,QAAQ,CAAU;QAClB,4BAAuB,GAAvB,uBAAuB,CAAQ;QAC/B,oBAAe,GAAf,eAAe,CAA2B;QAC1C,eAAU,GAAV,UAAU,CAAsB;QAEzC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,IAAI,CAAC,6BAA6B,GAAG,UAAU,CAAC;YAChD,IAAI,CAAC,eAAe,GAAG,IAAI,iDAAuB,CAChD,IAAI,CAAC,6BAA6B,EAClC,IAAI,CAAC,uBAAuB,CAC7B,CAAC;SACH;aAAM;YACL,IAAI,CAAC,eAAe,GAAG,IAAI,+CAAsB,EAAE,CAAC;SACrD;IACH,CAAC;CACF;AArBD,8DAqBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@opentelemetry/api-logs';\nimport { Resource } from '@opentelemetry/resources';\nimport { LogRecordProcessor } from '../LogRecordProcessor';\nimport { LogRecordLimits } from '../types';\nimport { NoopLogRecordProcessor } from '../export/NoopLogRecordProcessor';\nimport { MultiLogRecordProcessor } from '../MultiLogRecordProcessor';\n\nexport class LoggerProviderSharedState {\n  readonly loggers: Map<string, Logger> = new Map();\n  activeProcessor: LogRecordProcessor;\n  readonly registeredLogRecordProcessors: LogRecordProcessor[] = [];\n\n  constructor(\n    readonly resource: Resource,\n    readonly forceFlushTimeoutMillis: number,\n    readonly logRecordLimits: Required<LogRecordLimits>,\n    readonly processors: LogRecordProcessor[]\n  ) {\n    if (processors.length > 0) {\n      this.registeredLogRecordProcessors = processors;\n      this.activeProcessor = new MultiLogRecordProcessor(\n        this.registeredLogRecordProcessors,\n        this.forceFlushTimeoutMillis\n      );\n    } else {\n      this.activeProcessor = new NoopLogRecordProcessor();\n    }\n  }\n}\n"]}