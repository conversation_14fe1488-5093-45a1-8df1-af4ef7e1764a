{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,mDAAkD;AAAzC,gHAAA,cAAc,OAAA;AAIvB,0EAAyE;AAAhE,gIAAA,sBAAsB,OAAA;AAC/B,8EAA6E;AAApE,oIAAA,wBAAwB,OAAA;AAEjC,8EAA6E;AAApE,oIAAA,wBAAwB,OAAA;AACjC,gFAA+E;AAAtE,sIAAA,yBAAyB,OAAA;AAClC,uCAAqD;AAA5C,mHAAA,uBAAuB,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type {\n  LoggerProviderConfig,\n  LogRecordLimits,\n  BufferConfig,\n  BatchLogRecordProcessorBrowserConfig,\n} from './types';\nexport { LoggerProvider } from './LoggerProvider';\nexport type { SdkLogRecord } from './export/SdkLogRecord';\nexport type { LogRecordProcessor } from './LogRecordProcessor';\nexport type { ReadableLogRecord } from './export/ReadableLogRecord';\nexport { NoopLogRecordProcessor } from './export/NoopLogRecordProcessor';\nexport { ConsoleLogRecordExporter } from './export/ConsoleLogRecordExporter';\nexport type { LogRecordExporter } from './export/LogRecordExporter';\nexport { SimpleLogRecordProcessor } from './export/SimpleLogRecordProcessor';\nexport { InMemoryLogRecordExporter } from './export/InMemoryLogRecordExporter';\nexport { BatchLogRecordProcessor } from './platform';\n"]}