export type { LoggerProviderConfig, LogRecordLimits, BufferConfig, BatchLogRecordProcessorBrowserConfig, } from './types';
export { LoggerProvider } from './LoggerProvider';
export type { SdkLogRecord } from './export/SdkLogRecord';
export type { LogRecordProcessor } from './LogRecordProcessor';
export type { ReadableLogRecord } from './export/ReadableLogRecord';
export { NoopLogRecordProcessor } from './export/NoopLogRecordProcessor';
export { ConsoleLogRecordExporter } from './export/ConsoleLogRecordExporter';
export type { LogRecordExporter } from './export/LogRecordExporter';
export { SimpleLogRecordProcessor } from './export/SimpleLogRecordProcessor';
export { InMemoryLogRecordExporter } from './export/InMemoryLogRecordExporter';
export { BatchLogRecordProcessor } from './platform';
//# sourceMappingURL=index.d.ts.map