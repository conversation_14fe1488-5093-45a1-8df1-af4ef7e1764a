{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAoCH;;GAEG;AACU,QAAA,oBAAoB,GAAG,CAAC,UAAU,CAAC,CAAC;AAEjD;;GAEG;AACU,QAAA,mBAAmB,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as http from 'http';\nimport type * as https from 'https';\nimport { get, IncomingMessage, request } from 'http';\nimport * as url from 'url';\n\nexport type IgnoreMatcher = string | RegExp | ((url: string) => boolean);\nexport type HttpCallback = (res: IncomingMessage) => void;\nexport type RequestFunction = typeof request;\nexport type GetFunction = typeof get;\n\nexport type HttpCallbackOptional = HttpCallback | undefined;\n\n// from node 10+\nexport type RequestSignature = [http.RequestOptions, HttpCallbackOptional] &\n  HttpCallback;\n\nexport type HttpRequestArgs = Array<HttpCallbackOptional | RequestSignature>;\n\nexport type ParsedRequestOptions =\n  | (http.RequestOptions & Partial<url.UrlWithParsedQuery>)\n  | http.RequestOptions;\nexport type Http = typeof http;\nexport type Https = typeof https;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type Func<T> = (...args: any[]) => T;\n\nexport interface Err extends Error {\n  errno?: number;\n  code?: string;\n  path?: string;\n  syscall?: string;\n  stack?: string;\n}\n\n/**\n * Names of possible synthetic test sources.\n */\nexport const SYNTHETIC_TEST_NAMES = ['alwayson'];\n\n/**\n * Names of possible synthetic bot sources.\n */\nexport const SYNTHETIC_BOT_NAMES = ['googlebot', 'bingbot'];\n"]}