{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,qDAA2D;AAAlD,yHAAA,sBAAsB,OAAA;AAC/B,6DAA4D;AAAnD,oHAAA,gBAAgB,OAAA;AACzB,yDAAwD;AAA/C,gHAAA,cAAc,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { ExpressInstrumentation } from './instrumentation';\nexport { ExpressLayerType } from './enums/ExpressLayerType';\nexport { AttributeNames } from './enums/AttributeNames';\nexport type {\n  ExpressInstrumentationConfig,\n  ExpressRequestCustomAttributeFunction,\n  ExpressRequestInfo,\n  IgnoreMatcher,\n  LayerPathSegment,\n  SpanNameHook,\n} from './types';\n"]}