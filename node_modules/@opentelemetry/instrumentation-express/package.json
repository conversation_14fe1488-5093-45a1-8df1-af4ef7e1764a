{"name": "@opentelemetry/instrumentation-express", "version": "0.52.0", "description": "OpenTelemetry instrumentation for `express` http web application framework", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": {"type": "git", "url": "https://github.com/open-telemetry/opentelemetry-js-contrib.git", "directory": "packages/instrumentation-express"}, "scripts": {"test-all-versions": "tav", "test": "nyc mocha 'test/**/*.test.ts'", "tdd": "yarn test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../scripts/lint-readme.js", "version:update": "node ../../scripts/version-update.js", "setup:dev": "nx run-many -t compile -p @opentelemetry/instrumentation-express", "compile": "tsc -p .", "prepublishOnly": "npm run compile", "watch": "tsc -w"}, "keywords": ["express", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": "^18.19.0 || >=20.6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^2.0.0", "@opentelemetry/contrib-test-utils": "^0.49.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@types/express": "4.17.23", "@types/mocha": "10.0.10", "@types/node": "18.18.14", "@types/sinon": "17.0.4", "express": "^5.1.0", "nyc": "17.1.0", "rimraf": "5.0.10", "sinon": "15.2.0", "test-all-versions": "6.1.0", "typescript": "5.0.4"}, "dependencies": {"@opentelemetry/core": "^2.0.0", "@opentelemetry/instrumentation": "^0.203.0", "@opentelemetry/semantic-conventions": "^1.27.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/packages/instrumentation-express#readme", "gitHead": "e7960a2061c0a039ffa57ed8dbb73d605d65f4f6"}