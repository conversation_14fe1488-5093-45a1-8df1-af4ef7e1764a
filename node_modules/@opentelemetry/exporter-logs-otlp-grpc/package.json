{"name": "@opentelemetry/exporter-logs-otlp-grpc", "version": "0.203.0", "description": "OpenTelemetry Collector Exporter allows user to send collected log records to the OpenTelemetry Collector", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc mocha 'test/**/*.test.ts'", "version": "node ../../../scripts/version-update.js", "watch": "tsc --watch --build", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../../scripts/peer-api-check.js", "align-api-deps": "node ../../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "grpc", "tracing", "profiling", "metrics", "stats", "logging"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": "^18.19.0 || >=20.6.0"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "build/protos/**/*.proto", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@grpc/proto-loader": "^0.7.10", "@opentelemetry/api": "1.9.0", "@opentelemetry/api-logs": "0.203.0", "@opentelemetry/resources": "2.0.1", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/sinon": "17.0.4", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "11.1.0", "nyc": "17.1.0", "sinon": "18.0.1", "ts-loader": "9.5.2", "typescript": "5.0.4"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "2.0.1", "@opentelemetry/otlp-exporter-base": "0.203.0", "@opentelemetry/otlp-grpc-exporter-base": "0.203.0", "@opentelemetry/otlp-transformer": "0.203.0", "@opentelemetry/sdk-logs": "0.203.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/exporter-logs-otlp-grpc", "sideEffects": false, "gitHead": "93187f022457da152becc03dd00db8b2500702db"}